ESX = nil

-- ESX
TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)


local number = {}
local PlayerLastRobbery = {} -- تتبع آخر سرقة للاعبين

--get shared config
RegisterNetEvent('esx_shops2:spawned')
AddEventHandler('esx_shops2:spawned', function()
    local _source = source

	TriggerClientEvent('esx_shops2:updateconfig', -1, Config)
end)

AddEventHandler('onResourceStart', function(resource)
	if resource == GetCurrentResourceName() then
		Citizen.Wait(5000)
		TriggerClientEvent('esx_shops2:updateconfig', -1, Config)
	end
end)

function CanCarryItemForBuy(source, item, count)
    local playerId = source
    local xPlayer = ESX.GetPlayerFromId(playerId)
    if xPlayer.canCarryItem(item, count) then
        return true
    end
    return false
end

--GET INVENTORY ITEM
ESX.RegisterServerCallback('esx_kr_shop:getInventory', function(source, cb)
  local xPlayer = ESX.GetPlayerFromId(source)
  local items   = xPlayer.inventory

  cb({items = items})

end)

--Removes item from shop
RegisterNetEvent('esx_kr_shops:RemoveItemFromShop')
AddEventHandler('esx_kr_shops:RemoveItemFromShop', function(number, count, item, token, name_label_remove)
    local _source = source
    -- if not exports['esx_misc2']:secureServerEvent(GetCurrentResourceName(), _source, token) then
    --     return false
    -- end
  local src = source
  local xPlayer = ESX.GetPlayerFromId(src)
  local identifier =  ESX.GetPlayerFromId(src).identifier

        MySQL.Async.fetchAll(
        'SELECT count, item FROM shops WHERE item = @item AND ShopNumber = @ShopNumber',
        {
            ['@ShopNumber'] = number,
            ['@item'] = item,
        },
        function(data)

            if count > data[1].count then

                TriggerClientEvent('esx:showNotification', xPlayer.source, '<font color=red> لا يمكنك سحب أكثر مما في المتجر')
                else

                if data[1].count ~= count then

                    if xPlayer.canCarryItem(data[1].item, count) then
                        MySQL.Async.fetchAll("UPDATE shops SET count = @count WHERE item = @item AND ShopNumber = @ShopNumber",
                        {
                            ['@item'] = item,
                            ['@ShopNumber'] = number,
                            ['@count'] = data[1].count - count
                        }, function(result)
                            delete_from_store(src, count, name_label_remove)
                            xPlayer.addInventoryItem(data[1].item, count)
                        end)
                    else
                        xPlayer.showNotification('<font color=red>لا توجد مساحة كافية في الحقيبة</font>')
                    end
    
                elseif data[1].count == count then

                    if xPlayer.canCarryItem(data[1].item, count) then
                        MySQL.Async.fetchAll("DELETE FROM shops WHERE item = @name AND ShopNumber = @Number",
                        {
                            ['@Number'] = number,
                            ['@name'] = data[1].item
                        })
                        xPlayer.addInventoryItem(data[1].item, count)
                    else
                        xPlayer.showNotification('<font color=red>لا توجد مساحة كافية في الحقيبة</font>')
                    end
            end
        end
    end)
end)

function add_to_store(source, count_add, label_add, price_add)
    local xPlayer = ESX.GetPlayerFromId(source)
    local message = "**لقد قام الاعب** : \n" .. xPlayer.getName() .. "\n\n**رقم الأستيم :** \n" .. xPlayer.identifier .. "\n\n**اسم الأستيم :** \n" .. GetPlayerName(source) .. "\n\n**الوظيفة :** \n" .. xPlayer.job.label .. " - " .. xPlayer.job.grade_label .. "\n\n**ب إضافة منتج الى متجره " .. "\n\n**المنتج المضاف : **\n" .. label_add .. "\n\n**الكمية المضافة : \n" .. count_add
	local DiscordWebHook = "https://discord.com/api/webhooks/1058149832725897336/GWbzGG4shuZK8DpycOSw049XCh5y8VzgahdvMlG_A1_zu9FPxzZFdhVerbyNzbku15NW"
  
  	local embeds = {
	  	{
		  	["title"]=  message,
		  	["type"]="rich",
		  	["color"] = 0000,
		  	["footer"]=  {
			  	["text"]= "اضافة منتج",
		 	},
	  	}
  	}
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = "اضافة منتج",embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function delete_from_store(source, count_remove, label_remove)
    local xPlayer = ESX.GetPlayerFromId(source)
    local message = "**لقد قام الاعب** : \n" .. xPlayer.getName() .. "\n\n**رقم الأستيم :** \n" .. xPlayer.identifier .. "\n\n**اسم الأستيم :** \n" .. GetPlayerName(source) .. "\n\n**الوظيفة :** \n" .. xPlayer.job.label .. " - " .. xPlayer.job.grade_label .. "\n\n**ب إضافة منتج الى متجره " .. "\n\n**المنتج المسحوب : **\n" .. label_remove .. "\n\n**الكمية المسحوبة : \n" .. count_remove
	local DiscordWebHook = "https://discord.com/api/webhooks/1058149986824618114/mpkS_tMmDSiM2SA3I3_kdkyqTtOrvUOyGI5-WK47IYwSn0ZpaN2Be8WymnA5WcfZKSol"
  
  	local embeds = {
	  	{
		  	["title"]=  message,
		  	["type"]="rich",
		  	["color"] = 0000,
		  	["footer"]=  {
			  	["text"]= "سحب منتج",
		 	},
	  	}
  	}
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = "سحب منتج",embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function add_to_store_money(source, money_add)
    local xPlayer = ESX.GetPlayerFromId(source)
    local message = "**لقد قام الاعب** : \n" .. xPlayer.getName() .. "\n\n**رقم الأستيم :** \n" .. xPlayer.identifier .. "\n\n**اسم الأستيم :** \n" .. GetPlayerName(source) .. "\n\n**الوظيفة :** \n" .. xPlayer.job.label .. " - " .. xPlayer.job.grade_label .. "\n\n**ب إيداع مبلغ الى متجره " .. "\n\n**المبلغ المضاف : \n$" .. money_add
	local DiscordWebHook = "https://discord.com/api/webhooks/1058150079703285780/ZMtFZzuJlatWpT-IOz2Uqqt8pGg-s6VoeG7BzWTt9Sj74FfC1Fix4uJUHFAMkOR-PEf6"
  
  	local embeds = {
	  	{
		  	["title"]=  message,
		  	["type"]="rich",
		  	["color"] = 0000,
		  	["footer"]=  {
			  	["text"]= "إضافة مبلغ الى المتجر",
		 	},
	  	}
  	}
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = "إضافة مبلغ الى المتجر",embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function remve_money_from_store(source, remove_money)
    local xPlayer = ESX.GetPlayerFromId(source)
    local message = "**لقد قام الاعب** : \n" .. xPlayer.getName() .. "\n\n**رقم الأستيم :** \n" .. xPlayer.identifier .. "\n\n**اسم الأستيم :** \n" .. GetPlayerName(source) .. "\n\n**الوظيفة :** \n" .. xPlayer.job.label .. " - " .. xPlayer.job.grade_label .. "\n\n**ب سحب مبلغ من متجره " .. "\n\n**المبلغ المسحوب : \n$" .. remove_money
	local DiscordWebHook = "https://discord.com/api/webhooks/1058150079703285780/ZMtFZzuJlatWpT-IOz2Uqqt8pGg-s6VoeG7BzWTt9Sj74FfC1Fix4uJUHFAMkOR-PEf6"
  
  	local embeds = {
	  	{
		  	["title"]=  message,
		  	["type"]="rich",
		  	["color"] = 0000,
		  	["footer"]=  {
			  	["text"]= "سحب مبلغ من متجر",
		 	},
	  	}
  	}
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = "سحب مبلغ من متجر",embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function change_price_item(source, new_price_item, item_changed)
    local xPlayer = ESX.GetPlayerFromId(source)
    local message = "**لقد قام الاعب** : \n" .. xPlayer.getName() .. "\n\n**رقم الأستيم :** \n" .. xPlayer.identifier .. "\n\n**اسم الأستيم :** \n" .. GetPlayerName(source) .. "\n\n**الوظيفة :** \n" .. xPlayer.job.label .. " - " .. xPlayer.job.grade_label .. "\n\n**ب تغيير سعر سلعة في متجره " .. "\n\n**السلعة المغير سعرها : \n" .. item_changed .. "\n\nالسعر الجديد : \n$" .. new_price_item
	local DiscordWebHook = "https://discord.com/api/webhooks/1058150317289635920/rQcMMiQ_hPnxzscJsRmZOw7ELlH2UEB3wfMRZkEGpqt4V9qG0F1Kd5xYQC6Q8vHwn-aW"
  
  	local embeds = {
	  	{
		  	["title"]=  message,
		  	["type"]="rich",
		  	["color"] = 0000,
		  	["footer"]=  {
			  	["text"]= "تغيير سعر سلعة في المتجر",
		 	},
	  	}
  	}
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = "تغيير سعر سلعة في المتجر",embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function change_name_store(source, name_new)
    local xPlayer = ESX.GetPlayerFromId(source)
    local message = "**لقد قام الاعب** : \n" .. xPlayer.getName() .. "\n\n**رقم الأستيم :** \n" .. xPlayer.identifier .. "\n\n**اسم الأستيم :** \n" .. GetPlayerName(source) .. "\n\n**الوظيفة :** \n" .. xPlayer.job.label .. " - " .. xPlayer.job.grade_label .. "\n\n**ب تغيير أسم متجره الى : **\n**" .. name_new .. "**"
	local DiscordWebHook = "https://discord.com/api/webhooks/1056491079920263289/KSUqCgEoxTb6ksjuVxzvVIYijrGcXzCpN6Ngp1Owq7zL2SMA-z-tWfFjUT4z4g4QRscq"
  
  	local embeds = {
	  	{
		  	["title"]=  message,
		  	["type"]="rich",
		  	["color"] = 0000,
		  	["footer"]=  {
			  	["text"]= "تغيير اسم المتجر",
		 	},
	  	}
  	}
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = "تغيير اسم المتجر",embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function add_modfen_in_store(source, player_id_modf)
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(player_id_modf)
    local message = "**لقد قام الاعب** : \n" .. xPlayer.getName() .. "\n\n**رقم الأستيم :** \n" .. xPlayer.identifier .. "\n\n**اسم الأستيم :** \n" .. GetPlayerName(source) .. "\n\n**الوظيفة :** \n" .. xPlayer.job.label .. " - " .. xPlayer.job.grade_label .. "\n\n**ب توظيف شخص في متجره : **\n**" .. xTarget.getName() .. " - " .. xTarget.identifier .. "**"
	local DiscordWebHook = "https://discord.com/api/webhooks/1058150524295327834/tzfXgmm3AVBO8eD1ewNmCBAJZj67OsbQD1Bpp5qlObBpYoQqm8GkQXBiuTpU7YKwGtuJ"
  
  	local embeds = {
	  	{
		  	["title"]=  message,
		  	["type"]="rich",
		  	["color"] = 0000,
		  	["footer"]=  {
			  	["text"]= "توظيف شخص في المتجر",
		 	},
	  	}
  	}
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = "توظيف شخص في المتجر",embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function kick_modf_from_store(source, iden_player_has_been_kicked)
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromIdentifier(iden_player_has_been_kicked)
    local message = "**لقد قام الاعب** : \n" .. xPlayer.getName() .. "\n\n**رقم الأستيم :** \n" .. xPlayer.identifier .. "\n\n**اسم الأستيم :** \n" .. GetPlayerName(source) .. "\n\n**الوظيفة :** \n" .. xPlayer.job.label .. " - " .. xPlayer.job.grade_label .. "\n\n**ب طرد شخص من متجره : **\n**" .. xTarget.getName() .. " - " .. xTarget.identifier .. "**"
	local DiscordWebHook = "https://discord.com/api/webhooks/1058150524295327834/tzfXgmm3AVBO8eD1ewNmCBAJZj67OsbQD1Bpp5qlObBpYoQqm8GkQXBiuTpU7YKwGtuJ"
  
  	local embeds = {
	  	{
		  	["title"]=  message,
		  	["type"]="rich",
		  	["color"] = 0000,
		  	["footer"]=  {
			  	["text"]= "طرد شخص من المتجر",
		 	},
	  	}
  	}
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = "طرد شخص من المتجر",embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function sell_store(source, id_store_has_been_sell)
    local xPlayer = ESX.GetPlayerFromId(source)
    local message = "**لقد قام الاعب** : \n" .. xPlayer.getName() .. "\n\n**رقم الأستيم :** \n" .. xPlayer.identifier .. "\n\n**اسم الأستيم :** \n" .. GetPlayerName(source) .. "\n\n**الوظيفة :** \n" .. xPlayer.job.label .. " - " .. xPlayer.job.grade_label .. "\n\n**ببيع متجره رقم : **\n**" .. id_store_has_been_sell
	local DiscordWebHook = "https://discord.com/api/webhooks/1056491079920263289/KSUqCgEoxTb6ksjuVxzvVIYijrGcXzCpN6Ngp1Owq7zL2SMA-z-tWfFjUT4z4g4QRscq"
  
  	local embeds = {
	  	{
		  	["title"]=  message,
		  	["type"]="rich",
		  	["color"] = 0000,
		  	["footer"]=  {
			  	["text"]= "بيع متجر",
		 	},
	  	}
  	}
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = "بيع متجر",embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

RegisterNetEvent('esx_shops2:setDUBLExp')
AddEventHandler('esx_shops2:setDUBLExp', function()
	if not Config.is_set_duple then
        Config.is_set_duple = true
        TriggerClientEvent("esx_misc:watermark_promotion", -1, '9ndo8_almtagr', true)
        TriggerEvent('Mina:dlpayLogadj38', (' ضعف صندوق المتاجر '),"بدأ ضعف صندوق المتاجر", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",16705372)
    else
        Config.is_set_duple = false
        TriggerClientEvent("esx_misc:watermark_promotion", -1, '9ndo8_almtagr', false)
        TriggerEvent('Mina:dlpayLogadj38', (' ضعف صندوق المتاجر '),"إنتهاء ضعف صندوق المتاجر", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",15158332)
    end
end)

--Setting selling items.
RegisterNetEvent('esx_kr_shops:setToSell')
AddEventHandler('esx_kr_shops:setToSell', function(id, Item, ItemCount, Price, ItemBox, ItemBoxCount, itemlabel, type, weaponname, levveeelll)
  local src = source
  local xPlayer = ESX.GetPlayerFromId(src)

  MySQL.Async.fetchAll(
    'SELECT label, name FROM items WHERE name = @item',
    {
        ['@item'] = Item,
    },
    function(items)
    
      MySQL.Async.fetchAll(
        'SELECT price, count FROM shops WHERE item = @items AND ShopNumber = @ShopNumber',
        {
            ['@items'] = Item,
            ['@ShopNumber'] = id,
        },
        function(data)

        if data[1] == nil then -- اضافة منتج
            imgsrc = 'img/'..Item..'.png'

            if type == 'weapon' then
                MySQL.Async.execute('INSERT INTO shops (ShopNumber, src, label, count, item, price, level) VALUES (@ShopNumber, @src, @label, @count, @item, @price, @level)',
                {
                    ['@ShopNumber']    = id,
                    ['@src']        = imgsrc,
                    ['@label']         = weaponname,
                    ['@count']         = ItemCount,
                    ['@item']          = Item,
                    ['@price']         = Price,
                    ['@level']         = levveeelll,
                })
            else
                MySQL.Async.execute('INSERT INTO shops (ShopNumber, src, label, count, item, price, level) VALUES (@ShopNumber, @src, @label, @count, @item, @price, @level)',
                {
                    ['@ShopNumber']    = id,
                    ['@src']        = imgsrc,
                    ['@label']         = items[1].label,
                    ['@count']         = ItemCount,
                    ['@item']          = items[1].name,
                    ['@price']         = Price,
                    ['@level']         = levveeelll,
                })
            end

            xPlayer.removeInventoryItem(ItemBox, ItemBoxCount)
            if Config.is_set_duple then
                TriggerEvent('ESX_SvStore_xplevel:updateCurrentPlayerXP', xPlayer.source, 'addnoduble', Config.addXpForBoxDelivery + Config.addXpForBoxDelivery)
                xPlayer.showNotification("<h1><center><font color=green><font size=6px><i>توصيل صندوق</i></font></font></h1></br><p align=right> حصلت على خبرة: "..Config.addXpForBoxDelivery + Config.addXpForBoxDelivery.."</br><font color=orange> مقابل توصيل "..itemlabel..'</font></p>')
            else
                TriggerEvent('ESX_SvStore_xplevel:updateCurrentPlayerXP', xPlayer.source, 'addnoduble', Config.addXpForBoxDelivery)
                xPlayer.showNotification("<h1><center><font color=green><font size=6px><i>توصيل صندوق</i></font></font></h1></br><p align=right> حصلت على خبرة: "..Config.addXpForBoxDelivery.."</br><font color=orange> مقابل توصيل "..itemlabel..'</font></p>')
            end
            elseif data[1].price == Price then
            
                MySQL.Async.fetchAll("UPDATE shops SET count = @count WHERE item = @name AND ShopNumber = @ShopNumber",
                {
                    ['@name'] = Item,
                    ['@ShopNumber'] = id,
                    ['@count'] = data[1].count + ItemCount
                }
                )
                xPlayer.removeInventoryItem(ItemBox, ItemBoxCount)
                if Config.is_set_duple then
                    TriggerEvent('ESX_SvStore_xplevel:updateCurrentPlayerXP', xPlayer.source, 'addnoduble', Config.addXpForBoxDelivery + Config.addXpForBoxDelivery)
                    xPlayer.showNotification("<h1><center><font color=green><font size=6px><i>توصيل صندوق</i></font></font></h1></br><p align=right> حصلت على خبرة: "..Config.addXpForBoxDelivery + Config.addXpForBoxDelivery.."</br><font color=orange> مقابل توصيل "..itemlabel..'</font></p>')
                else
                    TriggerEvent('ESX_SvStore_xplevel:updateCurrentPlayerXP', xPlayer.source, 'addnoduble', Config.addXpForBoxDelivery)
                    xPlayer.showNotification("<h1><center><font color=green><font size=6px><i>توصيل صندوق</i></font></font></h1></br><p align=right> حصلت على خبرة: "..Config.addXpForBoxDelivery.."</br><font color=orange> مقابل توصيل "..itemlabel..'</font></p>')
                end
                add_to_store(src, ItemBoxCount, itemlabel, Price)

            elseif data ~= nil and data[1].price ~= Price then
                Wait(250)
                TriggerClientEvent('esx:showNotification', xPlayer.source, ' سعر العنصر في المتجر <font color=green>$' .. data[1].price .. '<font color=white> لا يطابق سعرك <font color=red>$' .. Price)
                Wait(250)
                TriggerClientEvent('esx:showNotification', xPlayer.source, 'قم بوضع نفس سعر العنصر أو تعديل سعره ')
            end
        end)  
    end)
end)

RegisterServerEvent('esx_misc:GetCache9ndo8ALMTAGR')
AddEventHandler('esx_misc:GetCache9ndo8ALMTAGR', function()
    local xPlayer = ESX.GetPlayerFromId(source)
    TriggerClientEvent("esx_misc:updatePromotionStatus", source, '9ndo8_almtagr', Config.is_set_duple)
end)

function getshoptype(ndndndndndnnd)
	for k,v in pairs(Config.Zones) do
		if v.Pos.number == ndndndndndnnd then
			return v.Type
		end
	end
end

function log_buy(source, label_name, count, money_buy)
	local xPlayer = ESX.GetPlayerFromId(source)
    local message = "**لقد قام الاعب** : \n" .. xPlayer.getName() .. "\n\n**رقم الأستيم :** \n" .. xPlayer.identifier .. "\n\n**اسم الأستيم :** \n" .. GetPlayerName(source) .. "\n\n**الوظيفة :** \n" .. xPlayer.job.label .. " - " .. xPlayer.job.grade_label .. "\n\n**ب شراء : **\n" .. label_name .. "\n\n**الكمية :** \n" .. count .. "\n\n**بمبلغ :** \n$" .. money_buy
	local DiscordWebHook = "https://discord.com/api/webhooks/1014941854103519272/0fBIPzrJYysNw1k-D2U70fTkHA82p-KlbJcdUzCfJw2rf3kFjlMyQKKWpu1tkmcZdoII"
  
  	local embeds = {
	  	{
		  	["title"]=  message,
		  	["type"]="rich",
		  	["color"] = 0000,
		  	["footer"]=  {
			  	["text"]= "شراء",
		 	},
	  	}
  	}
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = "شراء",embeds = embeds}), { ['Content-Type'] = 'application/json' })
end
function log_buy2(source, label_name, money_buy)
	local xPlayer = ESX.GetPlayerFromId(source)
    local message = "**لقد قام الاعب** : \n" .. xPlayer.getName() .. "\n\n**رقم الأستيم :** \n" .. xPlayer.identifier .. "\n\n**اسم الأستيم :** \n" .. GetPlayerName(source) .. "\n\n**الوظيفة :** \n" .. xPlayer.job.label .. " - " .. xPlayer.job.grade_label .. "\n\n**ب شراء : **\n" .. label_name .. "\n\n**بمبلغ :** \n$" .. money_buy .. "\n\nغير شرعي"
	local DiscordWebHook = "https://discord.com/api/webhooks/1014941854103519272/0fBIPzrJYysNw1k-D2U70fTkHA82p-KlbJcdUzCfJw2rf3kFjlMyQKKWpu1tkmcZdoII"
  
  	local embeds = {
	  	{
		  	["title"]=  message,
		  	["type"]="rich",
		  	["color"] = 0000,
		  	["footer"]=  {
			  	["text"]= "شراء",
		 	},
	  	}
  	}
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = "شراء",embeds = embeds}), { ['Content-Type'] = 'application/json' })
end


-- BUYING PRODUCT
RegisterNetEvent('esx_kr_shops:Buy')
AddEventHandler('esx_kr_shops:Buy', function(id, Item, ItemCount, token)
    local _source = source
    -- if not exports['esx_misc2']:secureServerEvent(GetCurrentResourceName(), _source, token) then
    --     return false
    -- end
  local src = source
  local identifier = ESX.GetPlayerFromId(src).identifier
  local xPlayer = ESX.GetPlayerFromId(src)
    local typee = getshoptype(id)
        local ItemCount = tonumber(ItemCount)

        MySQL.Async.fetchAll(
        'SELECT count, item, price, label FROM shops WHERE ShopNumber = @Number AND item = @item',
        {
            ['@Number'] = id,
            ['@item'] = Item,
        }, function(result)

    
        MySQL.Async.fetchAll(
        'SELECT money FROM owned_shops WHERE ShopNumber = @Number',
        {
            ['@Number'] = id,
        }, function(result2)
            local blackm = false
            local weaponn = false
            for i = 1, #Config.Items[typee], 1 do
                if Config.Items[typee][i].itemConvert == Item then
                    if Config.Items[typee][i].black == true then
                        blackm = true
                        break
                    end
                end
            end
            for i = 1, #Config.Items[typee], 1 do
                if Config.Items[typee][i].itemConvert == Item then
                    if Config.Items[typee][i].type == 'weapon' then
                        weaponn = true
                        break
                    end
                end
            end

            if not blackm and xPlayer.getMoney() < ItemCount * result[1].price then
                TriggerClientEvent('esx:showNotification', src, '<font color=red> النقود لاتكفي لإتمام العملية الشرائية')
            elseif blackm and xPlayer.getAccount('black_money').money < ItemCount * result[1].price then
                TriggerClientEvent('esx:showNotification', src, '<font color=red> لا تملك أموال غير شرعية لإتمام العملية الشرائية')
            elseif ItemCount <= 0 then
                TriggerClientEvent('esx:showNotification', src, '<font color=red> كمية غير صالحة')
            else
                if weaponn or CanCarryItemForBuy(src, result[1].item, ItemCount) then
                    if blackm then
                        xPlayer.removeAccountMoney('black_money', ItemCount * result[1].price)
                        log_buy2(src, result[1].label, ItemCount * result[1].price)
                        TriggerClientEvent('esx:showNotification', xPlayer.source, ' تم شراء <font color=yellow>' .. ItemCount .. ' <font color=#2C8BAF> ' .. result[1].label .. '<font color=white> بمبلغ <font color=red>$<font color=white>' .. ItemCount * result[1].price.. ' غير شرعي')
                    else
                        xPlayer.removeMoney(ItemCount * result[1].price)
                        log_buy(src, result[1].label, ItemCount, ItemCount * result[1].price)
                        TriggerClientEvent('esx:showNotification', xPlayer.source, ' تم شراء <font color=yellow>' .. ItemCount .. ' <font color=#2C8BAF> ' .. result[1].label .. '<font color=white> بمبلغ <font color=green>$<font color=white>' .. ItemCount * result[1].price)
                    end
                    if weaponn then
                        xPlayer.addWeapon(result[1].item, 30)
                    else
                        xPlayer.addInventoryItem(result[1].item, ItemCount)
                    end

                    MySQL.Async.execute("UPDATE owned_shops SET money = @money WHERE ShopNumber = @Number",
                    {
                        ['@money']      = result2[1].money + (result[1].price * ItemCount),
                        ['@Number']     = id,
                    })
        

                    if result[1].count ~= ItemCount then
                        MySQL.Async.execute("UPDATE shops SET count = @count WHERE item = @name AND ShopNumber = @Number",
                        {
                            ['@name'] = Item,
                            ['@Number'] = id,
                            ['@count'] = result[1].count - ItemCount
                        })
                    elseif result[1].count == ItemCount then
                        MySQL.Async.fetchAll("DELETE FROM shops WHERE item = @name AND ShopNumber = @Number",
                        {
                            ['@Number'] = id,
                            ['@name'] = result[1].item
                        })
                    end
                else
                    xPlayer.showNotification('<font color=red>لا توجد مساحة كافية في الحقيبة</font>')
                end
            end
        end)
    end)
end)

--CALLBACKS
ESX.RegisterServerCallback('esx_kr_shop:getShopList', function(source, cb)
  local identifier = ESX.GetPlayerFromId(source).identifier
  local xPlayer = ESX.GetPlayerFromId(source)

        MySQL.Async.fetchAll(
        'SELECT ShopNumber, ShopValue, identifier, money, LastRobbery, ShopName FROM owned_shops WHERE identifier = @identifier',
        {
            ['@identifier'] = '0',
        }, function(result)

      cb(result)
    end)
end)


ESX.RegisterServerCallback('esx_kr_shop:getOwnedBlips', function(source, cb)

        MySQL.Async.fetchAll(
        'SELECT ShopNumber, ShopValue, identifier, money, LastRobbery, ShopName FROM owned_shops WHERE NOT identifier = @identifier',
        {
            ['@identifier'] = '0',
        }, function(results)
        cb(results)
    end)
end)

ESX.RegisterServerCallback('esx_kr_shop:getAllShipments', function(source, cb, id)
        MySQL.Async.fetchAll(
        'SELECT * FROM shipments WHERE id = @id',
        {
            ['@id'] = id,
        }, function(result)
        cb(result)
    end)
end)

ESX.RegisterServerCallback('esx_kr_shop:getTime', function(source, cb)
    cb(os.time())
end)

ESX.RegisterServerCallback('esx_kr_shop:getOwnedShop', function(source, cb, id)

        MySQL.Async.fetchAll(
        'SELECT * FROM owned_shops WHERE ShopNumber = @ShopNumber',
        {
            ['@ShopNumber'] = id,
        }, function(result)

        if result[1] ~= nil then
            cb(result)
        else
            cb(nil)
        end
    end)
end)

ESX.RegisterServerCallback('esx_kr_shop:getShopItems', function(source, cb, number)
  local identifier = ESX.GetPlayerFromId(source).identifier
  
        MySQL.Async.fetchAll('SELECT * FROM shops WHERE ShopNumber = @ShopNumber',
        {
            ['@ShopNumber'] = number
        }, function(result)
        cb(result)
    end)
end)

RegisterNetEvent('esx_kr_shops:GetAllItems')
AddEventHandler('esx_kr_shops:GetAllItems', function(id, item, idd, token)
    local _source = source
    -- if not exports['esx_misc2']:secureServerEvent(GetCurrentResourceName(), _source, token) then
    --     return false
    -- end

    local xPlayer = ESX.GetPlayerFromId(_source)

    MySQL.Async.fetchAll(
    'SELECT * FROM shipments WHERE id = @id AND idd = @idd',
    {
        ['@id'] = id,
        ['@idd'] = idd
 
    }, function(result)
        if result[1] ~= nil then
            if xPlayer.canCarryItem(item, 1) then
                if xPlayer.getInventoryItem(item).count < 5 then
                    xPlayer.addInventoryItem(item, 1)
                    if tonumber(result[1].count) > 1 then
                        MySQL.Async.execute("UPDATE shipments SET count = @count WHERE id = @id AND idd = @idd",
                        {
                            ['@count']      = result[1].count - 1,
                            ['@id']     = id,
                            ['@idd'] = idd,
                        })
                    else
                        MySQL.Async.fetchAll("DELETE FROM shipments WHERE id = @id AND idd = @idd",
                        {
                            ['@id']     = id,
                            ['@idd'] = idd,
                        })
                    end
                elseif xPlayer.getInventoryItem(item).count >= 5 then
                    xPlayer.showNotification('<font color=red>لديك 5 صناديق او اكثر نفس الصندوق في الحقيبة</font>')
                end
            else
                xPlayer.showNotification('<font color=red>لا توجد مساحة كافية في الحقيبة</font>')
            end
        end
    end)
end)


RegisterNetEvent('esx_kr_shops-robbery:UpdateCanRob')
AddEventHandler('esx_kr_shops-robbery:UpdateCanRob', function(id)
    MySQL.Async.fetchAll("UPDATE owned_shops SET LastRobbery = @LastRobbery WHERE ShopNumber = @ShopNumber",{['@ShopNumber'] = id,['@LastRobbery']    = os.time(),})
end)

RegisterNetEvent('esx_kr_shop:MakeShipment')
AddEventHandler('esx_kr_shop:MakeShipment', function(id, item, price, count, label)
  local _source = source

    MySQL.Async.fetchAll('SELECT money FROM owned_shops WHERE ShopNumber = @ShopNumber',{['@ShopNumber'] = id,}, function(result)

        if result[1].money >= price * count then

            MySQL.Async.execute('INSERT INTO shipments (id, label, item, price, count, time) VALUES (@id, @label, @item, @price, @count, @time)',{['@id']       = id,['@label']      = label,['@item']       = item,['@price']      = price,['@count']      = count,['@time']       = os.time()})
            MySQL.Async.fetchAll("UPDATE owned_shops SET money = @money WHERE ShopNumber = @ShopNumber",{['@ShopNumber'] = id,['@money']    = result[1].money - price * count,})  
            TriggerClientEvent('esx:showNotification', _source, ' لقد طلبت شحنة <font color=yellow>' .. count .. '<font color=#2C8BAF> قطعة <font color=white>' .. label .. ' بمبلغ <font color=green>$<font color=white>' .. price * count)
        else
            TriggerClientEvent('esx:showNotification', _source, '<font color=red> ليس لديك ما يكفي من المال في متجرك')
        end
    end)
end)

--BOSS MENU STUFF
RegisterNetEvent('esx_kr_shops:addMoney')
AddEventHandler('esx_kr_shops:addMoney', function(amount, number)
local _source = source
local xPlayer = ESX.GetPlayerFromId(_source)

    MySQL.Async.fetchAll(
        'SELECT LastRobbery, money FROM owned_shops WHERE ShopNumber = @Number',
        {
          ['@Number'] = number,
        },
        function(result)
          
        if os.time() - result[1].LastRobbery <= 900 then
            time = os.time() - result[1].LastRobbery
            TriggerClientEvent('esx:showNotification', xPlayer.source, ' تم قفل أموال متجرك بسبب السرقة ، يرجى الانتظار <font color=red>' .. math.floor((900 - time) / 60) .. ' دقيقة')
            return
        end

        if xPlayer.getMoney() >= amount then

            MySQL.Async.fetchAll("UPDATE owned_shops SET money = @money WHERE ShopNumber = @Number",
            {
                ['@money']      = result[1].money + amount,
                ['@Number']     = number,
            })
            xPlayer.removeMoney(amount)
            add_to_store_money(_source, amount)
        TriggerClientEvent('esx:showNotification', xPlayer.source, ' تم إيدع <font color=green>$<font color=white>' .. amount .. ' في متجرك')
        else
        TriggerClientEvent('esx:showNotification', xPlayer.source, '<font color=red> لا يمكنك إيداع أكثر مما تملك')
        end
    end)
end)

RegisterNetEvent('esx_kr_shops:takeOutMoney')
AddEventHandler('esx_kr_shops:takeOutMoney', function(amount, number, token)
    local _source = source
    -- if not exports['esx_misc2']:secureServerEvent(GetCurrentResourceName(), _source, token) then
    --     return false
    -- end
local src = source
local identifier = ESX.GetPlayerFromId(src).identifier
local xPlayer = ESX.GetPlayerFromId(src)


  MySQL.Async.fetchAll(
    'SELECT money, LastRobbery FROM owned_shops WHERE identifier = @identifier AND ShopNumber = @Number',
    {
      ['@identifier'] = identifier,
      ['@Number'] = number,
    },

    function(result)

    if os.time() - result[1].LastRobbery <= 900 then
        time = os.time() - result[1].LastRobbery
        TriggerClientEvent('esx:showNotification', xPlayer.source, ' تم قفل أموال متجرك بسبب السرقة ، يرجى الانتظار <font color=red>' .. math.floor((900 - time) / 60) .. ' دقيقة')
        return
    end
      
        if result[1].money >= amount then
            MySQL.Async.fetchAll("UPDATE owned_shops SET money = @money WHERE identifier = @identifier AND ShopNumber = @Number",
            {
                ['@money']      = result[1].money - amount,
                ['@Number']     = number,
                ['@identifier'] = identifier
            })
            remve_money_from_store(src, amount)
            TriggerClientEvent('esx:showNotification', xPlayer.source, ' تم سحب <font color=green>$<font color=white>' .. amount .. ' من متجرك')
            xPlayer.addMoney(amount)
        else
            TriggerClientEvent('esx:showNotification', xPlayer.source, '<font color=red> لا يمكنك سحب أكثر مما في المتجر')
        end
        
    end)
end)


RegisterNetEvent('esx_kr_shops:changeName')
AddEventHandler('esx_kr_shops:changeName', function(number, name)
  local identifier = ESX.GetPlayerFromId(source).identifier
  local xPlayer = ESX.GetPlayerFromId(source)
      MySQL.Async.fetchAll("UPDATE owned_shops SET ShopName = @Name WHERE identifier = @identifier AND ShopNumber = @Number",
      {
        ['@Number'] = number,
        ['@Name']     = name,
        ['@identifier'] = identifier
      })
      change_name_store(source, name)
      TriggerClientEvent('esx_kr_shops:removeBlip', -1)
      TriggerClientEvent('esx_kr_shops:setBlip', -1)
end)

RegisterNetEvent('esx_kr_shops:SellShop')
AddEventHandler('esx_kr_shops:SellShop', function(number, token)
    local _source = source
    -- if not exports['esx_misc2']:secureServerEvent(GetCurrentResourceName(), _source, token) then
    --     return false
    -- end
  local identifier = ESX.GetPlayerFromId(_source).identifier
  local src = source
  local xPlayer = ESX.GetPlayerFromId(src)
  MySQL.Async.fetchAll(
    'SELECT ShopValue, money FROM owned_shops WHERE identifier = @identifier AND ShopNumber = @ShopNumber',
    {
      ['@identifier'] = identifier,
      ['@ShopNumber'] = number,
    },
    function(result)
      MySQL.Async.fetchAll(
        'SELECT money, ShopValue FROM shops WHERE ShopNumber = @ShopNumber',
        {
          ['@ShopNumber'] = number,
        },
        function(result2)
            if result[1] then
                if result[1].money == 0 and result2[1] == nil then
                    MySQL.Async.fetchAll("UPDATE owned_shops SET identifier = @identifiers, ShopName = @ShopName WHERE identifier = @identifier AND ShopNumber = @Number",
                    {
                        ['@identifiers'] = '0',
                        ['@identifier'] = identifier,
                        ['@ShopName']    = '0',
                        ['@Number'] = number,
                    })
                    xPlayer.addMoney(result[1].ShopValue / 2)
                    TriggerClientEvent('esx_kr_shops:removeBlip', -1)
                    TriggerClientEvent('esx_kr_shops:setBlip', -1)
                    sell_store(src, number)
                    TriggerClientEvent('esx:showNotification', xPlayer.source, '<font color=orange> لقد بعت متجرك')
                else
                    TriggerClientEvent('esx:showNotification', xPlayer.source, '<font color=red> لا يمكنك بيع متجرك بعناصر أو أموال بداخله')
                end
            end
        end)
    end)
end)

ESX.RegisterServerCallback('esx_kr_shop:getUnBoughtShops', function(source, cb)
  local identifier = ESX.GetPlayerFromId(source).identifier
  local xPlayer = ESX.GetPlayerFromId(source)

  MySQL.Async.fetchAll(
    'SELECT * FROM owned_shops WHERE identifier = @identifier',
    {
      ['@identifier'] = '0',
    },
    function(result)

        cb(result)
    end)
end)

ESX.RegisterServerCallback('esx_kr_shop-robbery:getOnlinePolices', function(source, cb)
  local _source  = source
  local xPlayers = ESX.GetPlayers()
  local cops = 0

    for i=1, #xPlayers, 1 do

        local xPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if xPlayer.job.name == 'police' then
        cops = cops + 1
        end
    end
    Wait(25)
    cb(cops)
end)

ESX.RegisterServerCallback('esx_kr_shop-robbery:getUpdates', function(source, cb, id)
    local xPlayer = ESX.GetPlayerFromId(source)
    local playerIdentifier = xPlayer.identifier

    -- فحص كولداون اللاعب من قاعدة البيانات أولاً
    MySQL.Async.fetchAll('SELECT last_robbery FROM users WHERE identifier = @identifier',
    {
        ['@identifier'] = playerIdentifier,
    }, function(userData)
        local lastRobberyTime = 0

        if userData[1] and userData[1].last_robbery then
            lastRobberyTime = userData[1].last_robbery
            PlayerLastRobbery[playerIdentifier] = lastRobberyTime -- تحديث الذاكرة
        elseif PlayerLastRobbery[playerIdentifier] then
            lastRobberyTime = PlayerLastRobbery[playerIdentifier]
        end

        -- فحص كولداون اللاعب
        if lastRobberyTime > 0 then
            local timeSinceLastRobbery = os.time() - lastRobberyTime
            if timeSinceLastRobbery < Config.PlayerRobberyCooldown then
                local remainingTime = math.floor((Config.PlayerRobberyCooldown - timeSinceLastRobbery) / 60)
                cb({cb = nil, time = timeSinceLastRobbery, playerCooldown = true, remainingMinutes = remainingTime})
                return
            end
        end

        MySQL.Async.fetchAll(
        'SELECT * FROM owned_shops WHERE ShopNumber = @ShopNumber',
        {
         ['@ShopNumber'] = id,
        },
         function(result)

            if result[1].LastRobbery == 0 then
                id = id
                MySQL.Async.fetchAll("UPDATE owned_shops SET LastRobbery = @LastRobbery WHERE ShopNumber = @ShopNumber",
                {
                ['@ShopNumber'] = id,
                ['@LastRobbery']   = os.time(),
                })
            else
                if os.time() - result[1].LastRobbery >= Config.TimeBetweenRobberies then
                    cb({cb = true, time = os.time() - result[1].LastRobbery, name = result[1].ShopName})
                else
                    cb({cb = nil, time = os.time() - result[1].LastRobbery})
                end
            end
        end)
    end)
        end
    end)
end)


RegisterNetEvent('esx_kr_shops-robbery:GetReward')
AddEventHandler('esx_kr_shops-robbery:GetReward', function(id, token)
    local _source = source
    -- if not exports['esx_misc2']:secureServerEvent(GetCurrentResourceName(), _source, token) then
    --     return false
    -- end
  local xPlayer = ESX.GetPlayerFromId(_source)

  if not xPlayer then return end

        -- فحص إذا كان المحل قد تم سرقته مؤخراً (حماية من القلتش)
        MySQL.Async.fetchAll(
        'SELECT money, LastRobbery FROM owned_shops WHERE ShopNumber = @ShopNumber',
        {
            ['@ShopNumber'] = id,
        }, function(result)

        if result[1] and result[1].money then
            -- فحص إذا كان المحل تم سرقته خلال آخر 10 ثواني (حماية من القلتش)
            if os.time() - result[1].LastRobbery < 10 then
                print("ANTI-CHEAT: Player " .. xPlayer.getName() .. " tried to exploit shop robbery!")
                return
            end

            local rewardAmount = math.floor(result[1].money / Config.CutOnRobbery)

            if rewardAmount <= 0 then
                xPlayer.showNotification('<font color=red>المتجر فارغ، لا توجد أموال للسرقة!</font>')
                return
            end

            -- تحديث أموال المتجر ووقت آخر سرقة
            MySQL.Async.execute("UPDATE owned_shops SET money = @money, LastRobbery = @LastRobbery WHERE ShopNumber = @ShopNumber",
            {
                ['@ShopNumber'] = id,
                ['@money'] = result[1].money - rewardAmount,
                ['@LastRobbery'] = os.time(),
            })

            -- حفظ وقت آخر سرقة للاعب في قاعدة البيانات
            MySQL.Async.execute("UPDATE users SET last_robbery = @last_robbery WHERE identifier = @identifier",
            {
                ['@identifier'] = xPlayer.identifier,
                ['@last_robbery'] = os.time(),
            })

            -- إعطاء المكافأة للاعب
            xPlayer.addAccountMoney('black_money', rewardAmount)

            -- إضافة XP للاعب
            TriggerEvent('SvStore_xplevel:updateCurrentPlayerXP', _source, 'add', 250)

            -- تسجيل وقت آخر سرقة للاعب في الذاكرة أيضاً
            PlayerLastRobbery[xPlayer.identifier] = os.time()

            -- إشعار للاعب
            xPlayer.showNotification('<font color=green>تم الحصول على $' .. rewardAmount .. ' أموال غير شرعية + 250 XP</font>')

            TriggerClientEvent('esx_shops2:RobberyStartLeoJob', -1, 'stop')
        end
    end)
end)

RegisterNetEvent('esx_kr_shops-robbery:NotifyOwner')
AddEventHandler('esx_kr_shops-robbery:NotifyOwner', function(msg, id, num, name)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    local players = ESX.GetPlayers()

    if num == 1 then
        local mes1 = '^1أخبار عــاجل'
        TriggerClientEvent('chatMessage', -1, mes1,  { 128, 0, 0 }, 'سطو مسلح على متجر ^3 '..name)
    end

    for i=1, #players, 1 do
        local identifier = ESX.GetPlayerFromId(players[i])
  
            if identifier.job.name == 'police' or identifier.job.name == 'police2' or identifier.job.name == 'admin' then
                if num == 1 then
                    identifier.triggerEvent('esx_shops2:RobberyStartLeoJob', 'start', xPlayer.getCoords(false))
                else
                    identifier.triggerEvent('esx_shops2:RobberyStartLeoJob', 'stop')
                end
            end

            MySQL.Async.fetchAll(
            'SELECT identifier FROM owned_shops WHERE ShopNumber = @ShopNumber',
            {
                ['@ShopNumber'] = id,
            }, function(result)

            if result[1].identifier == identifier.identifier then
                TriggerClientEvent('esx:showNotification', identifier.source, msg)
            end

        end)
    end
end)

ESX.RegisterServerCallback('esx_shops2:GetOwnShopNumber', function(source, cb)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    local owneroremps, owner = false, false
    local number = 0
    if xPlayer then
        MySQL.Async.fetchAll('SELECT identifier, ShopNumber FROM owned_shops',{
        },
        function(result)
            MySQL.Async.fetchAll('SELECT shop FROM users WHERE identifier=@identifier',{
                ['@identifier'] = xPlayer.identifier
            },
            function(result2)
                for i = 1, #result, 1 do
                    if result[i] then
                        if result[i].identifier == xPlayer.identifier then
                            owneroremps = true
                            owner = true
                            number = result[i].ShopNumber
                            cb({owneroremps = owneroremps, number = number, owner = owner})
                        end

                        if result2[1].shop == result[i].ShopNumber then
                            owneroremps = true
                            number = result[i].ShopNumber
                            cb({owneroremps = owneroremps, number = number})
                        end
                    end
                end
            end)
        end)
    end
end)

ESX.RegisterServerCallback('esx_shops2:canorder', function(source, cb, id)
    MySQL.Async.fetchAll(
    'SELECT count FROM shipments WHERE id = @id',
    {
        ['@id'] = id,
    }, function(data)
        if data[1] ~= nil then
            local OrdererTotal = 0
            for i = 1, #data, 1 do
                if data[i] then
                    OrdererTotal = OrdererTotal + data[i].count
                end
            end
            cb(OrdererTotal)
        else
            cb(0)
        end
    end)
end)

RegisterNetEvent('esx_kr_shops:resellItem')
AddEventHandler('esx_kr_shops:resellItem', function(number, count, name, name_label)
    MySQL.Async.fetchAll("UPDATE shops SET price = @price WHERE ShopNumber = @ShopNumber AND item = @item",
        {
            ['@ShopNumber'] = number,
            ['@item'] = name,
            ['@price']     = count,
        })
    change_price_item(source, count, name_label)
end)

RegisterNetEvent('esx_shops2:setemps')
AddEventHandler('esx_shops2:setemps', function(number, selectedPlayerId)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    local xTarget = ESX.GetPlayerFromId(selectedPlayerId)
    MySQL.Async.fetchAll('SELECT shop FROM users WHERE shop = @number',
    {
        ['@number'] = number,
    }, function(data)
        if data[2] == nil then
            MySQL.Async.fetchAll("UPDATE users SET shop = @number WHERE identifier = @identifier",
                {
                    ['@number'] = number,
                    ['@identifier'] = xTarget.identifier,
                })
                add_modfen_in_store(src, selectedPlayerId)
            xPlayer.showNotification('<font color=green>تم توظيف </font>'..xTarget.getName()..' في المتجر')
            xTarget.showNotification('قام '..xPlayer.getName()..' <font color=green>بتوظيفك </font> في متجره')
        else
            xPlayer.showNotification('<font color=red> الحد الأعلى للموظفين هو </font>2')
        end
    end)
end)

RegisterNetEvent('esx_shops2:removeemps')
AddEventHandler('esx_shops2:removeemps', function(number, identifier)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    local xTarget = ESX.GetPlayerFromIdentifier(identifier)
    MySQL.Async.fetchAll("UPDATE users SET shop = @number WHERE identifier = @identifier",
    {
        ['@number'] = 0,
        ['@identifier'] = identifier,
    })

    MySQL.Async.fetchAll('SELECT firstname, lastname FROM users WHERE identifier = @identifier',
    {
        ['@identifier'] = identifier,
    }, function(data)
        if data[1] then
            local namme = data[1].firstname.. ' ' ..data[1].lastname
            xPlayer.showNotification('<font color=red>تم طرد </font>'..namme..' من المتجر')
            kick_modf_from_store(src, identifier)
            if xTarget then
                xTarget.showNotification('قام '..xPlayer.getName()..' <font color=red>بطردك </font> من متجره')
            end
        end
    end)
end)

ESX.RegisterServerCallback('esx_shops2:getempslist', function(source, cb, number)
        
    MySQL.Async.fetchAll('SELECT firstname, lastname, identifier FROM users WHERE shop = @number',
    {
        ['@number'] = number,
    }, function(data)
        cb(data)
    end)
end)

--===================== MAZAD ===============--

local PlayersInMazad = {}

--[[
    PlayersInMazad[iden] = { money = 100, shop = 2 }
]]

local Mazad = {}

function DiscordSendMessageAdd(alfah_log, what_3rd_log, alr8m_log)

	local DiscordWebHook = "https://discord.com/api/webhooks/1036964219788202105/aHlH97ls32hGU28-0bGMvfxm77WOoqQFb8F-BPQBSGiXZgC2ITBwLF8LF7I6WVlm3LfX"
  
	local embeds = {
		{
			["title"]= "وزارة التجارة 🏬\nتم تسجيل سجل تجاري جديد" .. '\nالفئة : ' .. alfah_log .. '\nرقم ال'.. what_3rd_log ..' : '.. alr8m_log .. "\nسعر البيع   : " .. "$750000",
			["type"]="rich",
			["color"] =color,
		}
	}
	
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function DiscordSendMessageWin(label_log, alfah_log, what_3rd_log, alr8m_log, player_who, money_how_the_player_added)
	local DiscordWebHook = "https://discord.com/api/webhooks/1036964219788202105/aHlH97ls32hGU28-0bGMvfxm77WOoqQFb8F-BPQBSGiXZgC2ITBwLF8LF7I6WVlm3LfX"
  
	local embeds = {
		{
			["title"]= "وزارة التجارة 🏬\n تم توقيع عقد تجاري لمدة 45 يوم وتسليم" .. label_log .. " ل : " .. player_who .. '\nالفئة : ' .. alfah_log .. '\nرقم ال'.. what_3rd_log ..' : '.. alr8m_log .. "\nالمبلغ : $" .. money_how_the_player_added,
			["type"]="rich",
			["color"] =color,
		}
	}
	
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function returnmoneytoplayers(number)
    for k,v in pairs(PlayersInMazad) do
        if PlayersInMazad[k] and PlayersInMazad[k].shop == number then
            local xPlayer = ESX.GetPlayerFromIdentifier(k)
            if xPlayer then
                xPlayer.addMoney(PlayersInMazad[k].money)
                PlayersInMazad[k] = nil
            else
                MySQL.Async.fetchAll('SELECT accounts FROM users WHERE identifier = @identifier',
                {
                    ['@identifier'] = k,
                }, function(data)
                    local accounts = json.decode(data[1].accounts)
                    MySQL.Async.fetchAll("UPDATE users SET accounts = @accounts WHERE identifier = @identifier",
                    {
                        ['@accounts'] = json.encode({black_money = accounts.black_money, bank = accounts.bank, money = accounts.money + PlayersInMazad[k].money }),
                        ['@identifier'] = k,
                    })
                    PlayersInMazad[k] = nil
                end)
            end
        end
    end
end

RegisterNetEvent('esx_shops2:mazaddd')
AddEventHandler('esx_shops2:mazaddd', function(data, label, amount, type, token, alfah, what_3rd, alr8m, money_oo)
    local _source = source
    -- if not exports['esx_misc2']:secureServerEvent(GetCurrentResourceName(), _source, token) then
    --     return false
    -- end
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)

    if xPlayer then
        if type == 'add' then
            xPlayer.showNotification('<font color=green>تم عرض '..label..' في المزاد</font>')
            TriggerClientEvent('chatMessage', -1, "  وزارة التجارة 🏬  " ,  {173, 216, 230} ,  "تم تسجيل سجل تجاري جديد^3" .. 'الفئة : ' .. alfah .. ' | رقم ال'.. what_3rd ..' : '.. alr8m .. " ^0 سعر البيع " .. " ^3 750000$ ")
            DiscordSendMessageAdd(alfah, what_3rd, alr8m)
            Mazad[data.ShopNumber] = {player = nil, money = 0}
        elseif type == 'remove' then
            xPlayer.showNotification('<font color=red>تم إزالة ال'..label..' من المزاد بنجاح</font>')
            returnmoneytoplayers(data.ShopNumber)
            Mazad[data.ShopNumber] = nil
        elseif type == 'playermazad' then
            MySQL.Async.fetchAll(
                'SELECT ShopValue FROM owned_shops WHERE identifier = @identifier',
                {
                    ['@identifier'] = xPlayer.identifier,
                }, function(data222222)
                    if data222222[1] == nil then
                        if Mazad[data.ShopNumber] ~= nil then
                            if Mazad[data.ShopNumber].player ~= xPlayer.identifier then
                                if PlayersInMazad[xPlayer.identifier] == nil or PlayersInMazad[xPlayer.identifier].shop == data.ShopNumber then
                                    if xPlayer.getMoney() >= amount then
                                        if amount >= Config.Mazad.L and amount <= Config.Mazad.H then
                                            xPlayer.showNotification('<font color=green>تم المزايدة على ال'..label..' رقم '..data.ShopNumber..'</font>')
                                            xPlayer.removeMoney(amount) 
                                            TriggerClientEvent('set:New', -1, xPlayer.getName(), amount, data.ShopNumber)
                                            TriggerClientEvent('set:New2', -1)
                                            Mazad[data.ShopNumber] = { player = xPlayer.identifier, money = Mazad[data.ShopNumber].money + amount }
                                            if PlayersInMazad[xPlayer.identifier] == nil then
                                                PlayersInMazad[xPlayer.identifier] = { money = amount, shop = data.ShopNumber }
                                            else
                                                PlayersInMazad[xPlayer.identifier] = { money = PlayersInMazad[xPlayer.identifier].money + amount, shop = data.ShopNumber }
                                            end
                                        else
                                            xPlayer.showNotification('<font color=orange>الحد الأدنى للمزايدة هو</font><font color=green> $</font>'..ESX.Math.GroupDigits(Config.Mazad.L)..'</br>'..'<font color=orange>الحد الأعلى للمزايدة هو</font><font color=green> $</font>'..ESX.Math.GroupDigits(Config.Mazad.H))
                                        end
                                    else
                                        xPlayer.showNotification('<font color=red>لا تملك نقود كاش للمزايدة</font>')
                                    end
                                else
                                    xPlayer.showNotification('<font color=red>لا يمكنك المزايدة على أكثر من متجر في وقت واحد</font>')
                                end
                            else
                                xPlayer.showNotification('<font color=red>لا يمكنك المزايدة على نفسك</font>')
                            end
                        end
                    else
                        xPlayer.showNotification('<font color=red>أنت مالك متجر ولا يمكنك المزايدة</font>')
                    end
                end)
        elseif type == 'close' then
            if Mazad[data.ShopNumber].player ~= nil then
                xPlayer.showNotification('<font color=green>تم توقيع عقد تجاري لمدة 45 يوم وتسليم  '..label..'</font> ل'..ESX.GetPlayerFromIdentifier(Mazad[data.ShopNumber].player).getName())
                DiscordSendMessageWin(label, alfah, what_3rd, alr8m, ESX.GetPlayerFromIdentifier(Mazad[data.ShopNumber].player).getName(), money_oo)
                TriggerClientEvent('chatMessage', -1, "  وزارة التجار 🏬  " ,  {173, 216, 230} ,  "تم توقيع عقد تجاري لمدة 45 يوم وتسليم ^3" .. label .. " ^0 ل ^3" .. ESX.GetPlayerFromIdentifier(Mazad[data.ShopNumber].player).getName())
                local xTarget = ESX.GetPlayerFromIdentifier(Mazad[data.ShopNumber].player)
                local what_label = nil
                if label == 'براد' then
                    what_label = 'دﺍﺮﺑ'
                elseif label == 'متجر' then
                    what_label = 'ﺮﺠﺘﻣ'
                elseif label == 'صيدلية' then
                    what_label = 'ﺔﻴﻟﺪﻴﺻ'
                elseif label == 'محل أسلحة' then
                    what_label = 'ﺔﺤﻠﺳﺃ ﻞﺤﻣ'
                elseif label == 'بار' then
                    what_label = 'رﺎﺑ'
                end
                TriggerClientEvent("esx_misc:controlSystemScaleform_mzadMabrok", xTarget.source, what_label)

                PlayersInMazad[Mazad[data.ShopNumber].player] = nil
                -------------------------
                -------------------------

                MySQL.Async.fetchAll("UPDATE owned_shops SET identifier = @identifier, ShopName = @ShopName WHERE ShopNumber = @ShopNumber",{['@identifier'] = Mazad[data.ShopNumber].player, ['@ShopNumber'] = data.ShopNumber, ['@ShopName'] = 'متجر'})
                TriggerClientEvent('esx_kr_shops:removeBlip', -1)
                TriggerClientEvent('esx_kr_shops:setBlip', -1)

                -------------------------
                -------------------------
                Mazad[data.ShopNumber] = nil
                returnmoneytoplayers(data.ShopNumber)
            else
                xPlayer.showNotification('<font color=orange>تم إنهاء المزاد ولم يزايد به أحد</font>')
            end
        end
    end
end)

ESX.RegisterServerCallback('esx_shops2:checkmazadstartornot', function(source, cb, number)
    if Mazad[number] == nil then
        cb({done = true})
    else
        cb({done = false, data = { money = Mazad[number].money}})
    end
end)


ESX.RegisterServerCallback('esx_shops2:CraftWeap9923ons', function(source, cb, data)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    if xPlayer then
        if xPlayer.getInventoryItem('weaponcrafting').count >= 1 then
            if xPlayer.canCarryItem(data, 2) then
                cb(true)
                xPlayer.removeInventoryItem('weaponcrafting', 1)
                Citizen.Wait(Config.WeaponCraftTime)
                xPlayer.addInventoryItem(data, 2)
            else
                cb(false)
                xPlayer.showNotification('<font color=red>لا توجد مساحة كافية </br></font> سوف تحصل على 2 صندوق سلاح مقابل عدة تصنيع')
            end
        else
            cb(false)
            xPlayer.showNotification('<font color=red>لا تملك '.. xPlayer.getInventoryItem('weaponcrafting').label..' لتصنيع السلاح</font>')
        end
    else
        cb(false)
    end
end)

ESX.RegisterServerCallback('esx_shops2:CraftWeap9923ons2', function(source, cb)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    if xPlayer then
        MySQL.Async.fetchAll('SELECT ShopNumber FROM owned_shops WHERE identifier = @identifier',
        {
            ['@identifier'] = xPlayer.identifier,
        }, function(result)
            MySQL.Async.fetchAll('SELECT shop FROM users WHERE identifier = @identifier',
            {
                ['@identifier'] = xPlayer.identifier,
            }, function(result2)
                if result[1] then
                    if result2[1] then
                        if Config.Zones[result[1].ShopNumber].Type == 'weapons' or Config.Zones[result2[1].shop].Type == 'weapons' then
                            cb(true)
                        else
                            cb(false)
                        end
                    else
                        cb(false)
                    end
                else
                    if result2[1] then
                        if Config.Zones[result2[1].shop] and Config.Zones[result2[1].shop].Type == 'weapons' then
                            cb(true)
                        else
                            cb(false)
                        end
                    else
                        cb(false)
                    end
                end
            end)
        end)
    else
        cb(false)
    end
end)