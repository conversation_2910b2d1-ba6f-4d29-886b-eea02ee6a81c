$(document).ready(function() {
    // إخفاء الواجهة عند البداية
    $('#wrapper').hide();
    
    // متغيرات السلة
    const cartData = {
        cartCount: 0,
        cartTotal: 0,
        cartItems: []
    };

    // استقبال الرسائل من اللعبة
    window.addEventListener('message', function(event) {
        if (event.data.type === 'shop') {
            openShop(event.data);
        }
    });

    // فتح المتجر
    function openShop(data) {
        $('#wrapper').show().css('display', 'flex').show();
        $('#products-container').empty();
        
        setupShopHeader(data.type2, data.ShopName);
        setupBossActions(data.owner);
        loadProducts(data.result);
    }

    // إعداد رأس المتجر
    function setupShopHeader(shopType, shopName) {
        const shopTypes = {
            'market': { icon: 'fa-store', color: 'rgb(100,221,23)', prefix: 'متجر' },
            'bar': { icon: 'fa-glass-martini-alt', color: 'rgb(236,167,27)', prefix: 'بار' },
            'pharmacie': { icon: 'fa-prescription-bottle-alt', color: 'rgb(245,48,48)', prefix: 'صيدلية' },
            'rts': { icon: 'fa-utensils', color: 'rgb(236,119,16)', prefix: 'مـطـعـم' },
            'weapons': { icon: 'fa-crosshairs', color: 'rgb(124,0,190)', prefix: 'متجر أسلحة' },
            'SodaMachine': { icon: 'fa-tint', color: 'rgb(52, 152, 219)', prefix: 'براد' },
            'default': { icon: 'fa-shopping-basket', color: 'rgb(52, 152, 219)', prefix: 'متجر' }
        };
        
        const shopConfig = shopTypes[shopType] || shopTypes['default'];
        
        $('.logo span').html('<i class="fas ' + shopConfig.icon + '"></i> ' + shopConfig.prefix + ' - ' + shopName)
                      .css('color', shopConfig.color);
    }

    // إعداد أزرار الإدارة
    function setupBossActions(isOwner) {
        $('#boss-actions').html(isOwner ? '<button class="button" id="bossactions"><i class="fas fa-cogs"></i> إدارة المتجر</button>' : '');
    }

    // تحميل المنتجات
    function loadProducts(products) {
        if (!products || !products.length) return;
        
        for (let i = 0; i < products.length; i++) {
            const product = products[i];
            const productCard = $(`
                <div class="product-card" id="${product.item}" data-label="${product.label}" data-count="${product.count}" data-price="${product.price}">
                    <div class="product-image">
                        <img src="img/${product.item}.png" alt="${product.label}" loading="lazy" />
                    </div>
                    <div class="product-info">
                        <h4 class="product-name">${product.label}</h4>
                        <div class="product-details">
                            <div class="product-price">$${product.price}</div>
                            <h5>${product.price} للحبة</h5>
                            <h6>في المخزن: ${product.count}x</h6>
                        </div>
                        <button class="product-action add-to-cart"><i class="fas fa-cart-plus"></i> إضافة للسلة</button>
                    </div>
                </div>
            `);
            $('#products-container').append(productCard);
        }

        // إضافة منتج للسلة
        $('#products-container').find('.add-to-cart').on('click', function() {
            const productElement = $(this).closest('.product-card');
            const itemId = productElement.attr('id');
            const label = productElement.data('label');
            const count = productElement.data('count');
            const price = productElement.data('price');
            
            productElement.hide();
            cartData.cartCount++;
            updateCartBadge();
            
            $.post('http://esx_shops2/putcart', JSON.stringify({
                'item': itemId,
                'price': price,
                'label': label,
                'count': count
            }), function(response) {
                updateCartItems(response);
            });
        });
    }

    // تحديث شارة السلة
    function updateCartBadge() {
        $('.cart-badge').text(cartData.cartCount);
        $('.carticon').toggle(cartData.cartCount > 0);
    }

    // تحديث عناصر السلة
    function updateCartItems(items) {
        cartData.cartItems = items || [];
        const cartContainer = $('.cart-items');
        cartContainer.empty();

        if (!items || items.length === 0) {
            cartContainer.html(`
                <div class="empty-cart" style="text-align: center; padding: 20px;">
                    <i class="fas fa-shopping-cart" style="font-size: 50px; color: rgba(255,255,255,0.2); margin: 20px 0;"></i>
                    <p style="color: rgba(255,255,255,0.5);">السلة فارغة</p>
                </div>
            `);
            $('.cart-actions').hide();
            $('.subtotal-value, .tax-value, .total-value').text('$0.00');
            return;
        }

        for (let i = 0; i < items.length; i++) {
            const item = items[i];
            const cartItem = $(`
                <div class="cartitem" data-item="${item.item}" data-price="${item.price}">
                    <img src="img/${item.item}.png" alt="${item.label}" />
                    <div class="cart-item-details">
                        <h4>${item.label}</h4>
                        <h5>$${item.price}</h5>
                        <h6>الكمية: ${item.count}x</h6>
                        <div class="quantity-control" data-item="${item.item}" data-max="${item.count}">
                            <button class="quantity-button minus"><i class="fas fa-minus"></i></button>
                            <input type="text" class="quantity-input textareas" id="${item.item}" data-count="${item.count}" value="1">
                            <button class="quantity-button plus"><i class="fas fa-plus"></i></button>
                        </div>
                    </div>
                </div>
            `);
            cartContainer.append(cartItem);
        }

        $('.cart-actions').show();
        calculateTotal();
        setupQuantityControls();
        setupCartButtons();
    }

    // حساب المجموع
    function calculateTotal() {
        let subtotal = 0;
        $('.cartitem').each(function() {
            const quantity = parseInt($(this).find('.quantity-input').val()) || 0;
            const price = parseFloat($(this).data('price')) || 0;
            if (quantity > 0 && price > 0) {
                subtotal += quantity * price;
            }
        });

        const taxRate = 0.05;
        const tax = subtotal * taxRate;
        const total = subtotal + tax;

        $('.subtotal-value').text('$' + subtotal.toFixed(2));
        $('.tax-value').text('$' + tax.toFixed(2));
        $('.total-value').text('$' + total.toFixed(2));

        return { subtotal: subtotal, tax: tax, total: total };
    }

    // إعداد أزرار الكمية
    function setupQuantityControls() {
        $('.cart-items').off('click', '.quantity-button');
        
        $('.cart-items').on('click', '.quantity-button.minus', function() {
            const input = $(this).siblings('.quantity-input');
            let quantity = parseInt(input.val()) || 0;
            if (quantity > 1) {
                input.val(--quantity);
                calculateTotal();
            }
        });

        $('.cart-items').on('click', '.quantity-button.plus', function() {
            const input = $(this).siblings('.quantity-input');
            const maxQuantity = parseInt($(this).closest('.quantity-control').data('max'));
            let quantity = parseInt(input.val()) || 0;
            if (quantity < maxQuantity) {
                input.val(++quantity);
                calculateTotal();
            }
        });

        $('.cart-items').off('input', '.quantity-input').on('input', '.quantity-input', function() {
            calculateTotal();
        });
    }

    // إعداد أزرار السلة
    function setupCartButtons() {
        if (!$('#save-favorites').length) {
            $('.cart-actions').prepend(`
                <button id="save-favorites" class="save-favorites">
                    <i class="fas fa-heart"></i> حفظ كمفضلة
                </button>
            `);
        }
        
        if (localStorage.getItem('lastOrder') && !$('#repeat-last-order').length) {
            $('.cart-actions').prepend(`
                <button id="repeat-last-order" class="repeat-order">
                    <i class="fas fa-redo"></i> تكرار آخر طلب
                </button>
            `);
        }
    }

    // إغلاق المتجر
    function closeShop() {
        $('#wrapper').hide();
        cartData.cartCount = 0;
        updateCartBadge();
    }

    // الأحداث
    $(document).on('click', '.carticon', function() {
        $('#wrapper').hide();
        $('#cart').show();
    });

    $(document).on('click', '#back', function() {
        $('#cart').hide();
        $('#wrapper').show();
    });

    $(document).on('click', '#refreshcart', function() {
        $.post('http://esx_shops2/emptycart', JSON.stringify({}));
        $.post('http://esx_shops2/refresh', JSON.stringify({}));
        cartData.cartCount = 0;
        updateCartBadge();
        closeShop();
    });

    $(document).on('click', '#buybutton', function() {
        let hasValidItems = false;
        let totalCost = 0;

        $('.cartitem').each(function() {
            const input = $(this).find('.quantity-input');
            const itemId = input.attr('id');
            const quantity = input.val().trim();
            const isValid = !isNaN(quantity) && quantity !== '';
            const maxQuantity = input.data('count');
            const price = parseFloat($(this).data('price')) || 0;

            if (isValid && parseInt(maxQuantity) >= parseInt(quantity) && parseInt(quantity) > 0) {
                hasValidItems = true;
                totalCost += parseInt(quantity) * price;
                $.post('http://esx_shops2/buy', JSON.stringify({
                    'Count': quantity,
                    'Item': itemId,
                    'Price': price
                }));
            } else if (quantity !== '') {
                $.post('http://esx_shops2/notify', JSON.stringify({
                    'msg': '<font color=red>الكمية خاطئة</font>'
                }));
            }
        });

        if (hasValidItems) {
            saveLastOrder();
            $.post('http://esx_shops2/emptycart', JSON.stringify({ 'totalCost': totalCost }));
            closeShop();
        }
    });

    $(document).on('click', '#bossactions', function() {
        $.post('http://esx_shops2/bossactions', JSON.stringify({}));
        $.post('http://esx_shops2/escape', JSON.stringify({}));
        $.post('http://esx_shops2/emptycart', JSON.stringify({}));
        closeShop();
    });

    // البحث
    $('#search-items').on('keyup', function() {
        const searchTerm = $(this).val().toLowerCase();
        $('.add-to-cart').each(function() {
            const productName = $(this).data('label').toLowerCase();
            $(this).toggle(productName.indexOf(searchTerm) > -1);
        });
    });

    // مفتاح الهروب
    $(document).keydown(function(e) {
        if (e.which == 27) { // ESC key
            $.post('http://esx_shops2/escape', JSON.stringify({}));
            $.post('http://esx_shops2/emptycart', JSON.stringify({}));
            closeShop();
        }
    });

    // حفظ المفضلة
    function saveFavorites() {
        const favorites = [];
        $('.cartitem').each(function() {
            const itemId = $(this).data('item');
            const quantity = parseInt($(this).find('.quantity-input').val()) || 1;
            favorites.push({ 'item': itemId, 'quantity': quantity });
        });

        if (favorites.length > 0) {
            try {
                localStorage.setItem('favorites', JSON.stringify(favorites));
                showNotification('تم حفظ المنتجات المفضلة بنجاح!');
            } catch (error) {
                console.error('خطأ في حفظ المفضلة:', error);
            }
        }
    }

    // تحميل آخر طلب
    function loadLastOrder() {
        try {
            const lastOrder = JSON.parse(localStorage.getItem('lastOrder'));
            if (lastOrder && lastOrder.length > 0) {
                for (let i = 0; i < lastOrder.length; i++) {
                    const orderItem = lastOrder[i];
                    const cartItem = $('.cartitem[data-item="' + orderItem.item + '"]');
                    if (cartItem.length) {
                        cartItem.find('.quantity-input').val(orderItem.quantity);
                    }
                }
                calculateTotal();
                showNotification('تم تحميل آخر طلب بنجاح!');
            }
        } catch (error) {
            console.error('خطأ في تحميل آخر طلب:', error);
        }
    }

    // حفظ آخر طلب
    function saveLastOrder() {
        const lastOrder = [];
        $('.cartitem').each(function() {
            const itemId = $(this).data('item');
            const quantity = parseInt($(this).find('.quantity-input').val()) || 1;
            lastOrder.push({ 'item': itemId, 'quantity': quantity });
        });

        if (lastOrder.length > 0) {
            try {
                localStorage.setItem('lastOrder', JSON.stringify(lastOrder));
            } catch (error) {
                console.error('خطأ في حفظ الطلب:', error);
            }
        }
    }

    // إظهار الإشعار
    function showNotification(message) {
        let notification = $('#notification');
        if (!notification.length) {
            $('body').append('<div id="notification" class="notification"></div>');
            notification = $('#notification');
        }
        
        notification.text(message).removeClass('hide').addClass('show');
        setTimeout(function() {
            notification.removeClass('show').addClass('hide');
        }, 3000);
    }

    // أحداث المفضلة وآخر طلب
    $(document).on('click', '#save-favorites', saveFavorites);
    $(document).on('click', '#repeat-last-order', loadLastOrder);
});
