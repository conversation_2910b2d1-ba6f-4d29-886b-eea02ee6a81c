<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <title>متجر مدينة العزبة</title>
    
    <!-- مكتبات -->
    <script src="nui://game/ui/jquery.js" type="text/javascript"></script>
    <script src="//code.jquery.com/jquery-1.12.4.js"></script>
    <script src="//code.jquery.com/ui/1.12.1/jquery-ui.js"></script>

    <!-- ستايلات -->
    <link href="style.css" rel="stylesheet" type="text/css" />
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/smoothness/jquery-ui.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- إشعار مخصص -->
    <style>
        .notification {
            position: fixed;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(90deg, #ffc107, #ff9800);
            color: #222;
            padding: 15px 25px;
            border-radius: 12px;
            box-shadow: 0 0 20px rgba(255, 193, 7, 0.3);
            font-size: 17px;
            font-family: 'Cairo', sans-serif;
            font-weight: 700;
            opacity: 0;
            transition: opacity 0.4s ease-in-out;
            z-index: 9999;
            border: 2px solid #fff4d5;
        }

        .notification.show {
            opacity: 1;
        }
    </style>
</head>

<body>

    <!-- إشعار -->
    <div id="notification" class="notification"></div>

    <!-- الخلفية المظللة -->
    <div class="overlay"></div>

    <!-- أيقونة السلة -->
    <div id="carticon" class="carticon">
        <span class="cart-badge">0</span>
        <i class="fas fa-shopping-cart"></i>
    </div>

    <!-- واجهة المتجر -->
    <div id="wrapper" class="store-container">

        <!-- القائمة الجانبية -->
        <div class="sidebar">
            <div class="logo">
                <h2>متجر <span></span></h2>
            </div>
            <div class="store-details">
                <div class="store-name"></div>
                <div class="store-balance"></div>
            </div>
            <div class="sidebar-buttons"></div>
            <div id="boss-actions"></div>
        </div>

        <!-- قسم المنتجات -->
        <div class="products-area">
            <div class="products-header">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="search-items" placeholder="ابحث عن منتج...">
                </div>
            </div>

            <div class="products-grid" id="products-container"></div>
        </div>
    </div>

    <!-- نافذة السلة -->
    <div id="cart" class="cart-modal">
        <div class="cart-title">
            <h3>سلة المشتريات</h3>
            <button id="back" class="close-cart"><i class="fas fa-times"></i></button>
        </div>

        <div class="cart-items"></div>

        <div class="cart-summary">
            <div class="summary-row">
                <div class="summary-label">المجموع الفرعي</div>
                <div class="summary-value subtotal-value">$0.00</div>
            </div>
            <div class="summary-row">
                <div class="summary-label">الضريبة (5%)</div>
                <div class="summary-value tax-value">$0.00</div>
            </div>
            <div class="summary-row">
                <div class="summary-total-label">المجموع الكلي</div>
                <div class="summary-total-value total-value">$0.00</div>
            </div>
        </div>

        <div class="cart-actions">
            <button id="refreshcart" class="clear-cart"><i class="fas fa-trash"></i> إفراغ السلة</button>
            <button id="buybutton" class="checkout"><i class="fas fa-check"></i> إتمام الشراء</button>
        </div>
    </div>

    <!-- سكربت -->
    <script src="script.js" type="text/javascript"></script>
</body>
</html>
