local Keys = {
	["ESC"] = 322, ["F1"] = 288, ["F2"] = 289, ["F3"] = 170, ["F5"] = 166, ["F6"] = 167, ["F7"] = 168, ["F8"] = 169, ["F9"] = 56, ["F10"] = 57,
	["~"] = 243, ["1"] = 157, ["2"] = 158, ["3"] = 160, ["4"] = 164, ["5"] = 165, ["6"] = 159, ["7"] = 161, ["8"] = 162, ["9"] = 163, ["-"] = 84, ["="] = 83, ["BACKSPACE"] = 177,
	["TAB"] = 37, ["Q"] = 44, ["W"] = 32, ["E"] = 38, ["R"] = 45, ["T"] = 245, ["Y"] = 246, ["U"] = 303, ["P"] = 199, ["["] = 39, ["]"] = 40, ["ENTER"] = 18,
	["CAPS"] = 137, ["A"] = 34, ["S"] = 8, ["D"] = 9, ["F"] = 23, ["G"] = 47, ["H"] = 74, ["K"] = 311, ["L"] = 182,
	["LEFTSHIFT"] = 21, ["Z"] = 20, ["X"] = 73, ["C"] = 26, ["V"] = 0, ["B"] = 29, ["N"] = 249, ["M"] = 244, [","] = 82, ["."] = 81,
	["LEFTCTRL"] = 36, ["LEFTALT"] = 19, ["SPACE"] = 22, ["RIGHTCTRL"] = 70,
	["HOME"] = 213, ["PAGEUP"] = 10, ["PAGEDOWN"] = 11, ["DELETE"] = 178,
	["LEFT"] = 174, ["RIGHT"] = 175, ["TOP"] = 27, ["DOWN"] = 173,
	["NENTER"] = 201, ["N4"] = 108, ["N5"] = 60, ["N6"] = 107, ["N+"] = 96, ["N-"] = 97, ["N7"] = 117, ["N8"] = 61, ["N9"] = 118
}
ESX 			    			= nil
local showblip = false
local displayedBlips = {}
local AllBlips = {}
local number = nil
local BLOCKINPUTCONTROL = false
local name_new_the = nil
local name_new_the_s = {}
local money_lastet = nil
local data_number_store = nil
local is_las = nil
local iiis = {}
function getshoptype(ndndndndndnnd)
	for k,v in pairs(Config.Zones) do
		if v.Pos.number == ndndndndndnnd then
			return v.Type
		end
	end
end

local PlayerData = {}

configready = false
Config = nil
RegisterNetEvent('esx_shops2:updateconfig') 
AddEventHandler('esx_shops2:updateconfig', function(data) 
	Config = data 
	Wait(1000) 
	configready = true 
end)
Citizen.CreateThread(function()
	while ESX == nil do
		TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
		Citizen.Wait(0)
	end

	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(100)
	end

	PlayerData = ESX.GetPlayerData()
end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
	PlayerData = xPlayer
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	PlayerData.job = job
end)

AddEventHandler('playerSpawned', function()
	Citizen.Wait(5000)

	TriggerServerEvent('esx_shops2:spawned', securityToken)
end)

AddEventHandler('onResourceStop', function(resource)
	  if resource == GetCurrentResourceName() then
		  SetNuiFocus(false, false)
	  end
end)
  
RegisterNUICallback('escape', function(data, cb)

	  SetNuiFocus(false, false)

	  SendNUIMessage({
		  type = "close",
	  })

	  cb('ok')
end)

RegisterNUICallback('bossactions', function(data, cb)

	SetNuiFocus(false, false)

	SendNUIMessage({
		type = "close",
	})

	ESX.TriggerServerCallback('esx_shops2:GetOwnShopNumber', function(data)
		exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
		Citizen.SetTimeout(1000, function()
			if data.owneroremps then
				OpenBoss(data.owner)
			else
				ESX.ShowNotification('<font color=red>إدارة المتجر متاحة لصاحب المتجر والموظفين فقط</font>')
			end
		end)
	end)

	cb('ok')
end)

local Cart = {}

RegisterNUICallback('putcart', function(data, cb)
	table.insert(Cart, {item = data.item, label = data.label, count = data.count, id = data.id, price = data.price})
	cb(Cart)
end)

RegisterNUICallback('notify', function(data, cb)
	ESX.ShowNotification(data.msg)
	cb('ok')
end)

RegisterNUICallback('refresh', function(data, cb)

	Cart = {}

		ESX.TriggerServerCallback('esx_kr_shop:getOwnedShop', function(data)
			ESX.TriggerServerCallback('esx_kr_shop:getShopItems', function(result)

					if data ~= nil then
						Owner = true
					end

					if result ~= nil then
						local type2 = getshoptype(number)

						SetNuiFocus(true, true)

						SendNUIMessage({
							type = "shop",
							type2 = type2,
							result = result,
							ShopName = data[1].ShopName,
							owner = Owner,
						})
					end

				end, number)
			end, number)

	cb('ok')
end)

RegisterNUICallback('emptycart', function(data, cb)
	Cart = {}
	cb('ok')
end)

RegisterNUICallback('buy', function(data, cb)
	
	local type = getshoptype(number)
	
	for i=1, #Config.Items[type], 1 do
		if Config.Items[type][i].itemConvert == data.Item then
			if Config.Items[type][i].info then
				if exports.SvStore_xplevel.ESXP_GetRank() >= Config.Items[type][i].info.xp then
					if Config.Items[type][i].info.lisence then
						ESX.TriggerServerCallback('esx_license:checkLicense', function(hasLicense)
							if hasLicense then
								TriggerServerEvent('esx_kr_shops:Buy', number, data.Item, data.Count, securityToken)
							else
								ESX.ShowNotification('<font color=red>أنت لا تملك رخصة سلاح</font>')
							end
						end, GetPlayerServerId(PlayerId()), Config.Items[type][i].info.lisence)
					end
				else
					ESX.ShowNotification('<font color=red>الخبرة المطلوبة للسلعة </font><font color=orange>'..tostring(Config.Items[type][i].info.xp))
				end
			else
				TriggerServerEvent('esx_kr_shops:Buy', number, data.Item, data.Count, securityToken)
			end
		end
	end
	Cart = {}

	-- إغلاق القائمة بعد الشراء
	SetNuiFocus(false, false)
	SendNUIMessage({
		type = "close",
	})

	cb('ok')
end)

local ShopId           = nil
local Msg        = nil
local HasAlreadyEnteredMarker = false
local LastZone                = nil

Citizen.CreateThread(function()
	Citizen.Wait(500)
	local blip = AddBlipForCoord(835.69, -3193.38, 14.5)
	
	SetBlipSprite (blip, 473)
	SetBlipDisplay(blip, 4)
	SetBlipScale  (blip, 1.0)
	SetBlipColour (blip, 2)
	SetBlipAsShortRange(blip, true)
	BeginTextCommandSetBlipName("STRING")
	AddTextComponentString("<FONT FACE='sharlock'>ﻲﺴﻴﺋﺮﻟﺍ ﻉﺩﻮﺘﺴﻤﻟﺍ")
	EndTextCommandSetBlipName(blip)
end)

Citizen.CreateThread(function()
	Citizen.Wait(500)
	local blip = AddBlipForCoord(-120.18, -2505.69, 14.64)
	
	SetBlipSprite (blip, 473)
	SetBlipDisplay(blip, 4)
	SetBlipScale  (blip, 1.0)
	SetBlipColour (blip, 2)
	SetBlipAsShortRange(blip, true)
	BeginTextCommandSetBlipName("STRING")
	AddTextComponentString("<FONT FACE='sharlock'>ﻲﺑﺮﻐﻟﺍ ﻉﺩﻮﺘﺴﻤﻟﺍ")
	EndTextCommandSetBlipName(blip)
end)

Citizen.CreateThread(function()
	Citizen.Wait(500)
	local blip = AddBlipForCoord(232.94, 216.1, 157.62)
	
	SetBlipSprite (blip, 536)
	SetBlipDisplay(blip, 4)
	SetBlipScale  (blip, 0.80)
	SetBlipColour (blip, 59)
	SetBlipAsShortRange(blip, true)
	BeginTextCommandSetBlipName("STRING")
	AddTextComponentString("<FONT FACE='sharlock'>ﻱﺰﻛﺮﻤﻟﺍ ﻚﻨﺒﻟﺍ")
	EndTextCommandSetBlipName(blip)
end)

Citizen.CreateThread(function()
	Citizen.Wait(500)
	local blip = AddBlipForCoord(148.98, -1039.98, 49.22)
	
	SetBlipSprite (blip, 536)
	SetBlipDisplay(blip, 4)
	SetBlipScale  (blip, 0.80)
	SetBlipColour (blip, 2)
	SetBlipAsShortRange(blip, true)
	BeginTextCommandSetBlipName("STRING")
	AddTextComponentString("<FONT FACE='sharlock'>ﺪﻴﻬﺸﻟﺍ ﺔﻘﻳﺪﺣ ﻚﻨﺑ")
	EndTextCommandSetBlipName(blip)
end)

Citizen.CreateThread(function()
	Citizen.Wait(500)
	local blip = AddBlipForCoord(-1215.71, -333.07, 42.12)
	
	SetBlipSprite (blip, 536)
	SetBlipDisplay(blip, 4)
	SetBlipScale  (blip, 0.80)
	SetBlipColour (blip, 2)
	SetBlipAsShortRange(blip, true)
	BeginTextCommandSetBlipName("STRING")
	AddTextComponentString("<FONT FACE='sharlock'>( 1 ) ﻲﻋﺮﻔﻟﺍ ﺎﻧﻭﺰﻳﺭﺍ ﺎﻧﻭﺰﻳﺭﺍﺔﻌﻃﺎﻘﻣﻚﻨﺑ")
	EndTextCommandSetBlipName(blip)
end)

Citizen.CreateThread(function()
	Citizen.Wait(500)
	local blip = AddBlipForCoord(-349.17, -46.24, 90.91)
	
	SetBlipSprite (blip, 536)
	SetBlipDisplay(blip, 4)
	SetBlipScale  (blip, 0.80)
	SetBlipColour (blip, 2)
	SetBlipAsShortRange(blip, true)
	BeginTextCommandSetBlipName("STRING")
	AddTextComponentString("<FONT FACE='sharlock'>( 2 ) ﻲﻋﺮﻔﻟﺍ ﺎﻧﻭﺰﻳﺭﺍ ﺎﻧﻭﺰﻳﺭﺍﺔﻌﻃﺎﻘﻣﻚﻨﺑ")
	EndTextCommandSetBlipName(blip)
end)

Citizen.CreateThread(function()
	Citizen.Wait(500)
	local blip = AddBlipForCoord(314.46, -277.59, 91.08)
	
	SetBlipSprite (blip, 536)
	SetBlipDisplay(blip, 4)
	SetBlipScale  (blip, 0.80)
	SetBlipColour (blip, 2)
	SetBlipAsShortRange(blip, true)
	BeginTextCommandSetBlipName("STRING")
	AddTextComponentString("<FONT FACE='sharlock'>( 3 ) ﻲﻋﺮﻔﻟﺍ ﺎﻧﻭﺰﻳﺭﺍ ﺎﻧﻭﺰﻳﺭﺍﺔﻌﻃﺎﻘﻣﻚﻨﺑ")
	EndTextCommandSetBlipName(blip)
end)

Citizen.CreateThread(function()
	Citizen.Wait(500)
	local blip = AddBlipForCoord(-2959.27, 481.13, 25.67)
	
	SetBlipSprite (blip, 536)
	SetBlipDisplay(blip, 4)
	SetBlipScale  (blip, 0.80)
	SetBlipColour (blip, 2)
	SetBlipAsShortRange(blip, true)
	BeginTextCommandSetBlipName("STRING")
	AddTextComponentString("<FONT FACE='sharlock'>( 1 ) ﻲﻟﺎﻤﺸﻟﺍ ﺎﻧﻭﺰﻳﺭﺍ ﺎﻧﻭﺰﻳﺭﺍﺔﻌﻃﺎﻘﻣﻚﻨﺑ")
	EndTextCommandSetBlipName(blip)
end)

Citizen.CreateThread(function()
	Citizen.Wait(500)
	local blip = AddBlipForCoord(-109.23, 6464.49, 37.22)
	
	SetBlipSprite (blip, 536)
	SetBlipDisplay(blip, 4)
	SetBlipScale  (blip, 0.80)
	SetBlipColour (blip, 2)
	SetBlipAsShortRange(blip, true)
	BeginTextCommandSetBlipName("STRING")
	AddTextComponentString("<FONT FACE='sharlock'>ﻲﺴﻴﺋﺮﻟﺍ ﻮﺘﻴﻟﻮﺑ ﻚﻨﺑ")
	EndTextCommandSetBlipName(blip)
end)

Citizen.CreateThread(function()
	Citizen.Wait(500)
	local blip = AddBlipForCoord(1174.66, 2711.8, 42.85)
	
	SetBlipSprite (blip, 536)
	SetBlipDisplay(blip, 4)
	SetBlipScale  (blip, 0.85)
	SetBlipColour (blip, 2)
	SetBlipAsShortRange(blip, true)
	BeginTextCommandSetBlipName("STRING")
	AddTextComponentString("<FONT FACE='sharlock'>ﻲﺑﻮﻨﺠﻟﺍ ﻱﺪﻧﺎﺳ ﻚﻨﺑ")
	EndTextCommandSetBlipName(blip)
end)

Citizen.CreateThread(function()
	Citizen.Wait(500)
	local blip = AddBlipForCoord(5.23, -707.73, 222.73)
	
	SetBlipSprite (blip, 269)
	SetBlipDisplay(blip, 0)
	SetBlipScale  (blip, 1.30)
	SetBlipColour (blip, 70)
	SetBlipAsShortRange(blip, true)
	BeginTextCommandSetBlipName("STRING")
	AddTextComponentString("<FONT FACE='sharlock'>ﻲﻤﻟﺎﻌﻟﺍ ﺎﻧﻭﺰﻳﺭﺍ ﺎﻧﻭﺰﻳﺭﺍﺔﻌﻃﺎﻘﻣﻚﻨﺑ")
	EndTextCommandSetBlipName(blip)
end)

AddEventHandler('esx_kr_shop:hasEnteredMarker', function(zone)
	if zone == 'center' then
		ShopId = zone
		number = zone
		Msg  = _U('press_to_open_center')
	elseif zone == 'crafting' then
		ShopId = zone
		Msg  = '<font face="sharlock">ﺡﻼﺴﻟﺍ ﻊﻴﻨﺼﺘﻟ ~y~E ~w~ﻂﻐﺿﺇ'
	elseif zone <= 100 then
		ShopId = zone
		number = zone
		Msg  = _U('press_to_open')
	elseif zone >= 100 then
		ShopId = zone
		number = zone
		Msg  = _U('press_to_rob')
	end
end)

AddEventHandler('esx_kr_shop:hasEnteredMarker', function(zone)
	if zone == 'center' then
		ShopId = zone
		number = zone
		Msg  = _U('press_to_open_center')
	elseif zone == 'crafting' then
		ShopId = zone
		Msg  = '<font face="sharlock">ﺡﻼﺴﻟﺍ ﻊﻴﻨﺼﺘﻟ ~y~E ~w~ﻂﻐﺿﺇ'
	elseif zone <= 100 then
		ShopId = zone
		number = zone
		Msg  = _U('press_to_open')
	elseif zone >= 100 then
		ShopId = zone
		number = zone
		Msg  = _U('press_to_rob')
	end
end)

AddEventHandler('esx_kr_shop:hasExitedMarker', function(zone)
	BLOCKINPUTCONTROL = false
	ShopId = nil
end)

local TimerrrrkNdd = 0

Citizen.CreateThread(function (); while not configready do Citizen.Wait(1000) end
 	 while true do
		Citizen.Wait(0)

		if ShopId ~= nil then

			SetTextComponentFormat('STRING')
			AddTextComponentString(Msg)
			DisplayHelpTextFromStringLabel(0, 0, 1, -1)

				if IsControlJustReleased(0, Keys['E']) then
					if TimerrrrkNdd == 0 then
						TimerrrrkNdd = 1
						Citizen.SetTimeout(3000, function()
							TimerrrrkNdd = 0
						end)
						if ShopId == 'center' then
							exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
							Citizen.SetTimeout(1000, function()
								OpenShopCenter()
							end)
						elseif ShopId == 'crafting' then
							exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
							Citizen.SetTimeout(1000, function()
								CraftingWeapons()
							end)
						elseif ShopId <= 100 then
								ESX.TriggerServerCallback('esx_kr_shop:getOwnedShop', function(data)
									ESX.TriggerServerCallback('esx_kr_shop:getShopItems', function(result)
								
										if data ~= nil then
											Owner = true
										end
			
										if result ~= nil then
											local type2 = getshoptype(number)
											SetNuiFocus(true, true)
											exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
											Citizen.SetTimeout(1000, function()
												SendNUIMessage({
													type = "shop",
													type2 = type2,
													result = result,
													ShopName =  data[1].ShopName,
													owner = Owner,
												})
											end)
										end
					
									end, number)
								end, number)
						elseif ShopId >= 100 then
							exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
							Citizen.SetTimeout(1000, function()
								local panic = exports.esx_misc4:panicstate()
								if not panic['peace_time'].state and not panic['restart_time'].state then
									Robbery(number - 100)
								else
									if panic['peace_time'].state then
										ESX.ShowNotification('يمنع سرقة المتاجر أثناء إعلان<font color=800080> وقت راحة')
									else
										ESX.ShowNotification('يمنع سرقة المتاجر أثناء إعلان<font color=800000> وقت رستارت')
									end
								end
							end)	
						end
					end
				elseif IsControlJustReleased(0, Keys['H']) then
					ESX.TriggerServerCallback('esx_shops2:GetOwnShopNumber', function(data)
						exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
						Citizen.SetTimeout(1000, function()
							if data.owneroremps and number == data.number then
								OpenBoss(data.owner, data.number)
							else
								ESX.ShowNotification('<font color=red>إدارة المتجر متاحة لصاحب المتجر والموظفين فقط</font>')
							end
						end)
					end)
	 	 		end
		end
	end
 end)


function OpenBoss(isowner, number)


  	ESX.TriggerServerCallback('esx_kr_shop:getOwnedShop', function(data)
  
		local elements = {}
		local number = number
		table.insert(elements, {label = '<font color=#999999> رصيد المتجر : <font color=#00EE4F>$<font color=#ffffff>' .. data[1].money ,    value = ''})
		--table.insert(elements, {label = 'Shipments',    value = 'shipments'})
        table.insert(elements, {label = 'اضافة سلعة للبيع', value = 'putitem'})
		if isowner then
        	table.insert(elements, {label = 'سحب سلعة من المتجر',    value = 'takeitem'})
		end
        table.insert(elements, {label = '<font color=orange>ايداع</font> نقود في رصيد المتجر',    value = 'putmoney'})
		if isowner then
        	table.insert(elements, {label = '<font color=orange>سحب</font> نقود من رصيد المتجر',    value = 'takemoney'})
		end
		table.insert(elements, {label = 'تغيير سعر سلعة في المتجر',    value = 'resellitem'})
		if isowner then
			table.insert(elements, {label = 'إدارة الموظفين',    value = 'emps'})
			table.insert(elements, {label = '<font color=#F98A1B>تغيير اسم المتجر مقابل : <font color=#00EE4F>$<font color=#ffffff>' .. Config.ChangeNamePrice,    value = 'changename'})
			table.insert(elements, {label = '<font color=#CB120D>بيع متجرك مقابل : <font color=#00EE4F>$<font color=#ffffff>' .. math.floor(data[1].ShopValue / Config.SellValue),   value = 'sell'})
		end

		ESX.UI.Menu.Open(
		'default', GetCurrentResourceName(), 'boss',
		{
			title    = 'إدارة متجر <font color=gray>'..data[1].ShopName..'</font>',
			align    = 'bottom-right',
			elements = elements
		},
		function(data, menu)
        if data.current.value == 'putitem' then
			exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
			Citizen.SetTimeout(1000, function()
            	PutItem(number)
			end)
		elseif data.current.value == 'resellitem' then
			resellitem(number)
		elseif data.current.value == 'emps' then
			ESX.UI.Menu.Open(
			'default', GetCurrentResourceName(), 'emps',
			{
				title    = 'إدارة الموظفين',
				align    = 'bottom-right',
				elements = {
					{label = '<font color=green>توظيف</font>', value = 'add_emp'},
					{label = '<font color=red>طرد</font>', value = 'remove_emp'},
				}
			}, function(data2, menu2)
				if data2.current.value == 'add_emp' then
					-------------------------------------------
					-------------------------------------------
					--------------------------------------------
					local playerPed = PlayerPedId()
				local playersNearby = ESX.Game.GetPlayersInArea(GetEntityCoords(playerPed), 3.0)

				if #playersNearby > 0 then
					local players = {}
					elements = {}

					for k,playerNearby in ipairs(playersNearby) do
						players[GetPlayerServerId(playerNearby)] = true
					end

					ESX.TriggerServerCallback('esx:getPlayerNames', function(returnedPlayers)
						for playerId,playerName in pairs(returnedPlayers) do
							table.insert(elements, {
								label = playerName,
								playerId = playerId
							})
						end

						ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'add_emp', {
							title    = 'اختر الشخص المراد توظيفه',
							align    = 'bottom-right',
							elements = elements
						}, function(data2, menu2)
							local selectedPlayer, selectedPlayerId = GetPlayerFromServerId(data2.current.playerId), data2.current.playerId
							playersNearby = ESX.Game.GetPlayersInArea(GetEntityCoords(playerPed), 3.0)
							playersNearby = ESX.Table.Set(playersNearby)

							if playersNearby[selectedPlayer] then
								local selectedPlayerPed = GetPlayerPed(selectedPlayer)

								if IsPedOnFoot(selectedPlayerPed) and not IsPedFalling(selectedPlayerPed) then
									TriggerServerEvent('esx_shops2:setemps', number, selectedPlayerId)
								else
									ESX.ShowNotification('لايمكن توظيف اي شخص داخل المركبة')
								end
							else
								ESX.ShowNotification('<font color=red>لايوجد لاعب قريب منك</font>')
								menu2.close()
							end
						end, function(data2, menu2)
							menu2.close()
						end)
					end, players)
				else
					ESX.ShowNotification('<font color=red>لايوجد لاعب قريب منك</font>')
				end
					-------------------------------------------
					-------------------------------------------
					-------------------------------------------
				elseif data2.current.value == 'remove_emp' then
					ESX.TriggerServerCallback('esx_shops2:getempslist', function(data) 
							local elements3 = {}
							for i = 1, #data, 1 do
								if data[i] then
									table.insert(elements3, { label = data[i].firstname.. ' ' ..data[i].lastname, value = data[i].identifier })
								end
							end
							ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'remove_emp', {
								title    = 'اختر الشخص المراد طرده',
								align    = 'bottom-right',
								elements = elements3
							}, function(data2, menu2)
								TriggerServerEvent('esx_shops2:removeemps', number, data2.current.value)
								ESX.UI.Menu.CloseAll()
							end, function(data2, menu2)
								menu2.close()
							end)
					end, number)
				end
			end, function(data2, menu2)
				menu2.close()
			end)
        elseif data.current.value == 'takeitem' then  
			exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
			Citizen.SetTimeout(1000, function()
            	TakeItem(number)
			end)
        elseif data.current.value == 'takemoney' then
            

            ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'takeoutmoney', {
                title = 'كم تريد أن تسحب'
            }, function(data2, menu2)
  
			local amount = tonumber(data2.value)
			
			TriggerServerEvent('esx_kr_shops:takeOutMoney', amount, number, securityToken)
			
			menu2.close()
        
		end,
		function(data2, menu2)
		menu2.close()
		end)

	 	elseif data.current.value == 'putmoney' then
			ESX.UI.Menu.CloseAll()

			ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'putinmoney', {
			title = 'كم تريد أن تودع؟'
			}, function(data3, menu3)
			local amount = tonumber(data3.value)
			TriggerServerEvent('esx_kr_shops:addMoney', amount, number)
			menu3.close()
				end,
				function(data3, menu3)
			menu3.close()
		end)

		elseif data.current.value == 'sell' then
		  ESX.UI.Menu.CloseAll()    

		  ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'sell', {
			title = 'اكتب: ( نعم ) بدون أقواس للتأكيد'
          }, function(data4, menu4)
            
            if data4.value == 'نعم' then
              TriggerServerEvent('esx_kr_shops:SellShop', number, securityToken)
              menu4.close()
			end
		    	end,
		    	function(data4, menu4)
		    menu4.close()
		end)

	  elseif data.current.value == 'changename' then
		ESX.UI.Menu.CloseAll()    

		ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'changename', {
		  title = 'ماذا تريد تسمية متجرك؟'
        }, function(data5, menu5)
            
            TriggerServerEvent('esx_kr_shops:changeName', number, data5.value)
            menu5.close()
               		end,
                	function(data5, menu5)
                	menu5.close()
				end)

				end
        		end,
        		function(data, menu)
        	menu.close()
	    end)
    end, number)
end

function GetAllShipments(id)

	local elements = {}

	ESX.TriggerServerCallback('esx_kr_shop:getTime', function(time)
	ESX.TriggerServerCallback('esx_kr_shop:getAllShipments', function(items)

	local once = true
	local once2 = true

		for i=1, #items, 1 do

			if time - items[i].time >= Config.DeliveryTime and once2 then
			table.insert(elements, {label = '<font color=white>-- <font color=green>طلبات جاهزة للتسليم<font color=white> --'})
			once2 = false
			end

			if time - items[i].time >= Config.DeliveryTime then
			table.insert(elements, {label = '<font color=white>'..items[i].label..'<font color=gray>[<font color=orange>'..items[i].count..'<font color=gray>]', value = items[i].item, price = items[i].price, idd = items[i].idd })
			end

			if time - items[i].time <= Config.DeliveryTime and once then
				table.insert(elements, {label = '<font color=white>--<font color=red>طلبات جاري شحنها للمتجر<font color=white>--'})
				once = false
			end

			if time - items[i].time <= Config.DeliveryTime then
				times = time - items[i].time
				table.insert(elements, {label = '<font color=orange>'..items[i].label .. ' - <font color=#1B76F9> الكمية : <font color=white>'..items[i].count .. ' - <font color=#1B76F9> الوقت المتبقي : <font color=white>' .. math.floor((Config.DeliveryTime - times) / 60) .. ' دقيقة' })
			end

		end

	ESX.UI.Menu.Open(
	'default', GetCurrentResourceName(), 'allshipments',
	{
	  title    = '<font color=#ffffff>المستودع العام - <font color=orange>تتبع شحنات',
	  align    = 'bottom-right',
	  elements = elements
	},
	  function(data, menu)
		TriggerServerEvent('esx_kr_shops:GetAllItems', id, data.current.value, data.current.idd, securityToken)
		end,
		function(data, menu)
		menu.close()
		end)

	end, id)
	end)
end

function OpenShipmentDelivery(id)
	ESX.UI.Menu.CloseAll()
	local elements = {}
	local type = getshoptype(id)

		for i=1, #Config.Items[type], 1 do
			if Config.Items[type][i].Storge == nil or Config.Items[type][i].Storge ~= false then
				table.insert(elements, {labels =  Config.Items[type][i].label, label =  '<font color=white> '.. Config.Items[type][i].label .. '<font color=red> | <font color=green> $<font color=white>' .. Config.Items[type][i].price..'<font color=red> | <font color=orange>'..Config.Items[type][i].count..'</font><font color=gray> في الصندوق </font>',	value = Config.Items[type][i].item, price = Config.Items[type][i].price})
			end
		end

		ESX.UI.Menu.Open(
			'default', GetCurrentResourceName(), 'shipitem',
			{
			title    = '<font color=#ffffff>المستودع العام - <font color=orange>طلب منتجات جديدة',
			align    = 'bottom-right',
			elements = elements
			},
			function(data, menu)
				menu.close()
				ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'krille', {
				title = 'كم تريد شراء؟'
				}, function(data2, menu2)
					menu2.close()
					ESX.TriggerServerCallback('esx_shops2:canorder', function(canorder)
						print(canorder .. ' = '..data2.value ..' = '..Config.BoxMax[type])
						if (canorder + tonumber(data2.value)) <= Config.BoxMax[type] then
							TriggerServerEvent('esx_kr_shop:MakeShipment', id, data.current.value, data.current.price, tonumber(data2.value), data.current.labels)
						else
							ESX.ShowNotification('<font color=red>لا يمكن طلب أكثر من <font color=orange>'..Config.BoxMax[type]..'</font> صندوق</font>')
						end
					end, id)
				end, function(data2, menu2)
					menu2.close()
				end)

		end,
		function(data, menu)
		menu.close()
	end)
end


function TakeItem(number2)

  local elements = {}

  ESX.TriggerServerCallback('esx_kr_shop:getShopItems', function(result)

	for i=1, #result, 1 do
	    if result[i].count > 0 then
	    	table.insert(elements, {name_label_remove_from_store = result[i].label, label = '<font color=gray>'..result[i].label..'</font> | <font color=orange>'..result[i].count..'</font><font color=gray> في المتجر</font> | <font color=green>$'..result[i].price..'</font>', value = 'removeitem', ItemName = result[i].item})
	    end
    end


  ESX.UI.Menu.Open(
  'default', GetCurrentResourceName(), 'takeitem',
  {
	title    = 'إدارة المتجر',
	align    = 'bottom-right',
	elements = elements
  },
  function(data, menu)
local name = data.current.ItemName

    if data.current.value == 'removeitem' then
        menu.close()
        ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'howmuch', {
        title = 'كم تريد أن تأخذ؟'
        }, function(data2, menu2)

        local count = tonumber(data2.value)
		menu2.close()
    	TriggerServerEvent('esx_kr_shops:RemoveItemFromShop', number2, count, name, securityToken, data.current.name_label_remove_from_store)
    
			end, function(data2, menu2)
				menu2.close()
			end)
			end
		end,
		function(data, menu)
		menu.close()
		end)
  	end, number2)
end

--resellitem
function resellitem(number2)

	local elements = {}
  
	ESX.TriggerServerCallback('esx_kr_shop:getShopItems', function(result)
  
	  for i=1, #result, 1 do
		  if result[i].count > 0 then
			  table.insert(elements, {label_name_in_elem = result[i].label, label = '<font color=gray>'..result[i].label..'</font> | <font color=orange>'..result[i].count..'</font><font color=gray> في المتجر</font> | <font color=green>$'..result[i].price..'</font>', value = 'resellitem', ItemName = result[i].item})
		  end
	  end
  
  
	ESX.UI.Menu.Open(
	'default', GetCurrentResourceName(), 'resellitem',
	{
	  title    = 'تغيير سعر سلعة',
	  align    = 'bottom-right',
	  elements = elements
	},
	function(data, menu)
  local name = data.current.ItemName
  
	  if data.current.value == 'resellitem' then
		  menu.close()
		  ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'resellitem2', {
		  title = 'السعر الجديد'
		  }, function(data2, menu2)
  
		  local count = ESX.Math.Round(tonumber(data2.value))
		  menu2.close()
		  TriggerServerEvent('esx_kr_shops:resellItem', number2, count, name, data.current.label_name_in_elem)
		  ESX.ShowNotification('<font color=green>تم تغيير سعر المنتج بنجاح</font>')
	  
			  end, function(data2, menu2)
				  menu2.close()
			  end)
			  end
		  end,
		  function(data, menu)
		  menu.close()
		  end)
		end, number2)
  end


function PutItem(number2)

	local type = getshoptype(number2)

  local elements = {}

  ESX.TriggerServerCallback('esx_kr_shop:getInventory', function(result)
    for i=1, #result.items, 1 do
        
      local invitem = result.items[i]
      
	    if invitem.count > 0 then
			table.insert(elements, { label = '<font color=gray>'..invitem.label .. '</font> | <font color=orange>' .. invitem.count .. '</font><font color=gray> في الحقيبة </font>', count = invitem.count, name = invitem.name})
	    end
	end

  ESX.UI.Menu.Open(
  'default', GetCurrentResourceName(), 'putitem',
  {
	title    = 'إدارة المتجر',
	align    = 'bottom-right',
	elements = elements
  },
  function(data, menu)

        local itemName = data.current.name
        local invcount = data.current.count

			ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'sell', {
			title = _U('how_much')
			}, function(data2, menu2)

			local count = tonumber(data2.value)
		
			if count > invcount then
				ESX.ShowNotification('<font color=red>لا يمكنك بيع أكثر مما تملك')
				menu2.close()
				menu.close()
			else
				menu2.close()
				menu.close()

				for i = 1, #Config.Items[type], 1 do
					if Config.Items[type][i].item == itemName then
						if Config.Items[type][i].instore == nil or Config.Items[type][i].instore ~= false then
							local ccount = (Config.Items[type][i].count * count)
							local tyyppeeep = Config.Items[type][i].type or false
							local weaponsname = ESX.GetWeaponLabel(Config.Items[type][i].itemConvert) or false
							local levveell = 0

							if Config.Items[type][i].info then
								levveell = Config.Items[type][i].info.xp
							end

							if not tyyppeeep then
								ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'sellprice', {
									title = _U('set_price')
									}, function(data3, menu3)
										local done = false
										local price = tonumber(data3.value)
										menu3.close()
										TriggerServerEvent('esx_kr_shops:setToSell', number2, Config.Items[type][i].itemConvert, ccount, price, itemName, count, Config.Items[type][i].label, tyyppeeep, weaponsname, levveell)
									end)
							else
								TriggerServerEvent('esx_kr_shops:setToSell', number2, Config.Items[type][i].itemConvert, ccount, Config.Items[type][i].info.price, itemName, count, Config.Items[type][i].label, tyyppeeep, weaponsname, levveell)
							end
							done = true
							break
						end
					end
				end

				if done ~= true then
					ESX.ShowNotification('<font color=red>لا يمكن عرض هذه السلعة</font>')
				end
			end
				end,
				function(data3, menu3)
				menu3.close()
				end)
			end, 
			function(data2, menu2)
			menu2.close()
			end)
        end, function(data, menu)
        menu.close()
    end)
end


Citizen.CreateThread(function (); while not configready do Citizen.Wait(1000) end
  while true do
	Citizen.Wait(1)

	local coords = GetEntityCoords(PlayerPedId())
	local letsleep = true

		for k,v in pairs(Config.Zones) do
			if(27 ~= -1 and GetDistanceBetweenCoords(coords, v.Pos.x, v.Pos.y, v.Pos.z, true) < 20.0 ) then
				letsleep = false
				if v.Pos.red then
					DrawMarker(23, v.Pos.x, v.Pos.y, v.Pos.z + 0.05, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 0.2, 180, 0, 0, 200, false, true, 2, false, false, false, false)
					DrawMarker(29, v.Pos.x, v.Pos.y, v.Pos.z + 1.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 180, 0, 0, 200, false, true, 2, false, false, false, false)
				elseif v.Pos.craft then	
					DrawMarker(1, v.Pos.x, v.Pos.y, v.Pos.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5, 1.5, 0.5, 180, 0, 0, 200, false, true, 2, false, false, false, false)
				else
					DrawMarker(23, v.Pos.x, v.Pos.y, v.Pos.z + 0.05, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 0.2, 0, 180, 0, 200, false, true, 2, false, false, false, false)
					DrawMarker(29, v.Pos.x, v.Pos.y, v.Pos.z + 1.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 0, 180, 0, 200, false, true, 2, false, false, false, false)
				end
	        end
	    end

		if letsleep then
			Citizen.Wait(500)
		end
    end
end)


Citizen.CreateThread(function (); while not configready do Citizen.Wait(1000) end
  while true do
	Citizen.Wait(25)

	local coords      = GetEntityCoords(PlayerPedId())
	local isInMarker  = false
	local currentZone = nil
	local letsleep = true

	for k,v in pairs(Config.Zones) do
	  if(GetDistanceBetweenCoords(coords, v.Pos.x, v.Pos.y, v.Pos.z, true) < 1.2) then
		letsleep = false
		isInMarker  = true
		currentZone = v.Pos.number
	  end
	end

	if (isInMarker and not HasAlreadyEnteredMarker) or (isInMarker and LastZone ~= currentZone) then
	  HasAlreadyEnteredMarker = true
	  LastZone                = currentZone
	  TriggerEvent('esx_kr_shop:hasEnteredMarker', currentZone)
	end

	if not isInMarker and HasAlreadyEnteredMarker then
	  HasAlreadyEnteredMarker = false
	  TriggerEvent('esx_kr_shop:hasExitedMarker', LastZone)
	end

	if letsleep then
		Citizen.Wait(500)
	end
  end
end)

RegisterNetEvent('esx_kr_shops:setBlip')
AddEventHandler('esx_kr_shops:setBlip', function()

  	ESX.TriggerServerCallback('esx_kr_shop:getOwnedBlips', function(blips)

		if blips ~= nil then
			createBlip(blips)
	  	end
   	end)
end)

RegisterNetEvent('esx_kr_shops:removeBlip')
AddEventHandler('esx_kr_shops:removeBlip', function()

	for i=1, #displayedBlips do
    	RemoveBlip(displayedBlips[i])
	end

end)

AddEventHandler('playerSpawned', function(spawn)
	Citizen.Wait(500)

	ESX.TriggerServerCallback('esx_kr_shop:getOwnedBlips', function(blips)

		if blips ~= nil then
			createBlip(blips)
		end
	end)
end)



Citizen.CreateThread(function(); while not configready do Citizen.Wait(1000) end
	Citizen.Wait(500)

	ESX.TriggerServerCallback('esx_kr_shop:getOwnedBlips', function(blips)

		if blips ~= nil then
			createBlip(blips)
		end
	end)
end)

function createBlip(blips)
	while not configready do Citizen.Wait(1000) end
	for i=1, #displayedBlips do
    	RemoveBlip(displayedBlips[i])
		displayedBlips[i] = nil
	end
	for i=1, #blips, 1 do
  		for k,v in pairs(Config.Zones) do
			if v.Pos.number == blips[i].ShopNumber then
				local blip = AddBlipForCoord(vector3(v.Pos.x, v.Pos.y, v.Pos.z))
				if v.Type == 'market' then -- بقالة 
					SetBlipSprite (blip, 52)
					SetBlipColour (blip, 2)
					SetBlipDisplay(blip, 4)
				elseif v.Type == 'pharmacie' then -- صيدلية
					SetBlipSprite (blip, 153)
					SetBlipColour (blip, 1)
					SetBlipDisplay(blip, 4)
				elseif v.Type == 'rts' then -- المطاعم
					SetBlipSprite (blip, 628)
					SetBlipColour (blip, 46)
					SetBlipDisplay(blip, 2)
				elseif v.Type == 'bar' then -- بار
					SetBlipSprite (blip, 93)
					SetBlipColour (blip, 50)
					SetBlipDisplay(blip, 4)
				elseif v.Type == 'weapons' then -- محل اسلحة
					SetBlipSprite (blip, 110)
					SetBlipColour (blip, 64)
					SetBlipDisplay(blip, 4)
				elseif v.Type == 'SodaMachine' then -- براد
					SetBlipSprite (blip, 619)
					SetBlipColour (blip, 64)
					SetBlipDisplay(blip, 5)
				end
					SetBlipScale  (blip, 1.2)
					SetBlipAsShortRange(blip, true)
					BeginTextCommandSetBlipName("STRING")
					AddTextComponentString(blips[i].ShopName)
                    EndTextCommandSetBlipName(blip)
					table.insert(displayedBlips, blip)
			end
 		end
	end
end


function createForSaleBlips()
	if showblip then

		IDBLIPS = {
			[1] = {x = 373.875,   y = 325.896,  z = 102.566, n = 1},
			[2] = {x = 2557.458,  y = 382.282,  z = 107.622, n = 2},
			[3] = {x = -3038.939, y = 585.954,  z = 6.908, n = 3},
			[4] = {x = -1487.553, y = -379.107,  z = 39.163, n = 4},
			[5] = {x = 1392.562,  y = 3604.684,  z = 33.980, n = 5},
			[6] = {x = -2968.243, y = 390.910,   z = 14.043, n = 6},
			[7] = {x = 2678.916,  y = 3280.671, z = 54.241, n = 7},
			[8] = {x = -48.519,   y = -1757.514, z = 28.421, n = 8},
			[9] = {x = 1163.373,  y = -323.801,  z = 68.205, n = 9},
			[10] = {x = -707.501,  y = -914.260,  z = 18.215, n = 10},
			[11] = {x = -1820.523, y = 792.518,   z = 137.118, n = 11},
			[12] = {x = 1698.388,  y = 4924.404,  z = 41.063, n = 12},
			[13] = {x = 1961.464,  y = 3740.672, z = 31.343, n = 13},
			[14] = {x = 1135.808,  y = -982.281,  z = 45.415, n = 14},
			[15] = {x = 25.88,     y = -1347.1,   z = 28.5, n = 15},
			[16] = {x = -1393.409, y = -606.624,  z = 29.319, n = 16},
			[17] = {x = 547.431,   y = 2671.710, z = 41.156, n = 17},
			[18] = {x = -3241.927, y = 1001.462, z = 11.830, n = 18},
			[19] = {x = 1166.024,  y = 2708.930,  z = 37.157, n = 19},
			[20] = {x = 1729.216,  y = 6414.131, z = 34.037, n = 20},
		}

		for i=1, #IDBLIPS, 1 do

			local blip2 = AddBlipForCoord(vector3(IDBLIPS[i].x, IDBLIPS[i].y, IDBLIPS[i].z))
				
				SetBlipSprite (blip2, 52)
				SetBlipDisplay(blip2, 4)
				SetBlipScale  (blip2, 0.8)
				SetBlipColour (blip2, 1)
				SetBlipAsShortRange(blip2, true)
				BeginTextCommandSetBlipName("STRING")
				AddTextComponentString('ID: ' .. IDBLIPS[i].n)
				EndTextCommandSetBlipName(blip2)
				table.insert(AllBlips, blip2)
		end

		else
			for i=1, #AllBlips, 1 do
				RemoveBlip(AllBlips[i])
			end
		ESX.UI.Menu.CloseAll()
	end
end

--ROBBERY

local Var = nil
local Coordss = nil
local OnRobbery = false
local Id = nil
local Name = nil

function Robbery(id)	

    -- TriggerServerEvent('esx_kr_shops:UpdateCurrentShop', id)

	ESX.TriggerServerCallback('esx_kr_shop-robbery:getUpdates', function(result)
		ESX.TriggerServerCallback('esx_kr_shop-robbery:getOnlinePolices', function(results)

			if result.playerCooldown then
				ESX.ShowNotification("<font color=red> لا يمكنك سرقة متجر آخر ، يرجى الانتظار " .. result.remainingMinutes .. ' دقيقة ')
			elseif result.cb ~= nil then
				if results >= Config.RequiredPolices then
					TriggerEvent('esx_status:getStatus', 'drunk', function(status)
						if status.val >= 250000 then
							TriggerServerEvent('esx_kr_shops-robbery:UpdateCanRob', id)

							local coords = {
								x = Config.Zones[id].Object.x,
								y = Config.Zones[id].Object.y,
								z = Config.Zones[id].Object.z,
							}
								TriggerServerEvent('esx_phone:send', "police", "Shop robbery at the " .. result.name .. '\'s shop', true, coords)
								-- TriggerServerEvent("esx_misc:NoCrimetime", "StoresHave", false, 15)
								-- TriggerServerEvent("esx_misc:NoCrimetime", "StoresHave", true)
								TriggerServerEvent('Mody:Log:StoreLog', ""..result.name.."", "Mohammed")
								TriggerServerEvent('esx_kr_shops-robbery:NotifyOwner', "<font color=red>متجرك <font color=gray>(" .. result.name .. ')<font color=red> تحت السطو', id, 1, result.name)

								ESX.Game.SpawnObject(**********, coords, function(safe)
								SetEntityHeading(safe,  Config.Zones[id].Object.h)
								FreezeEntityPosition(safe, true)

								SetEntityHealth(safe, 7000)
								OnRobbery = true
								Var = safe
								Id = id
								Coordss = coords
								Name = result.name
								end)
						else
							ESX.ShowNotification('<font color=red> يجب أن تكون في حالة سكر بنسبة %25 لسرقة المتجر </font>')
						end
					end)
                else
					ESX.ShowNotification("<font color=red> لا يوجد عدد كافي من الشرطة " .. results .. '/' .. Config.RequiredPolices)
				end
			else
				ESX.ShowNotification("<font color=orange> هذا المتجر قد تعرض للسرقة بالفعل ، يرجى الانتظار" ..  math.floor((Config.TimeBetweenRobberies - result.time)  / 60) .. ' دقيقة ')
			end
		end, 'police')
	end, id)
end




Citizen.CreateThread(function()
	while true do
        Wait(0)
			local playerpos = GetEntityCoords(PlayerPedId())
			local letsleep = true
				if OnRobbery and GetDistanceBetweenCoords(playerpos.x, playerpos.y, playerpos.z, Coordss.x, Coordss.y, Coordss.z, true) <= 15 then
					letsleep = false
					local hp = GetEntityHealth(Var)
					TriggerEvent("mt:missiontext", '<font face="sharlock">ﺔﻧﺰﺨﻟﺍ ﺮﺴﻛ:~r~ ' .. hp/100 .. "%", 1000)

					if hp == 0 then
						OnRobbery = false
						TriggerServerEvent('esx_kr_shops-robbery:GetReward', Id, securityToken)
						TriggerServerEvent("esx_kr_shops-robbery:NotifyOwner", '<font color=red>تم سرقة متجرك <font color=gray>(' .. Name ..')<font color=red> للأسف', Id, 2, Name)
						DeleteEntity(Var)
					end

				elseif OnRobbery and GetDistanceBetweenCoords(playerpos.x, playerpos.y, playerpos.z, Coordss.x, Coordss.y, Coordss.z, true) >= 15 then
					letsleep = false
					OnRobbery = false
					DeleteEntity(Var)
					TriggerServerEvent('esx_kr_shops-robbery:NotifyOwner', "<font color=red>محاولة سرقة في متجرك <font color=gray>(" .. Name .. ')<font color=green> فاشلة', Id, 3, Name)
					ESX.ShowNotification(_U("robbery_cancel"))	
				end
				
				if letsleep then
					Citizen.Wait(500)
				end
	end
end)

RegisterNetEvent("mt:missiontext") -- credits: https://github.com/schneehaze/fivem_missiontext/blob/master/MissionText/missiontext.lua
AddEventHandler("mt:missiontext", function(text, time)
		ClearPrints()
		SetTextEntry_2("STRING")
		AddTextComponentString(text)
		DrawSubtitleTimed(time, 1)
end)



--------------------
--	   Storge	  --
--------------------

Citizen.CreateThread( function(); while not configready do Citizen.Wait(1000) end
		while true do
			local letsleep = true
			for	i = 1, #Config.Storge, 1 do
				local ped = PlayerPedId()
				local PedLocation = GetEntityCoords(ped)
				local MarkkerNumbber = 10
				local raduis = GetDistanceBetweenCoords(PedLocation, Config.Storge[i].pos.x, Config.Storge[i].pos.y, Config.Storge[i].pos.z, true)
				if raduis <= 35.0 then
					letsleep = false
					if i == 1 then MarkkerNumbber = 11 end;if i == 2 then MarkkerNumbber = 12 end;if i == 3 then MarkkerNumbber = 13 end;if i == 4 then MarkkerNumbber = 14 end;if i == 5 then MarkkerNumbber = 15 end;if i == 6 then MarkkerNumbber = 16 end;if i == 7 then MarkkerNumbber = 17 end;if i == 8 then MarkkerNumbber = 18 end;if i == 9 then MarkkerNumbber = 19 end;
					DrawMarker(MarkkerNumbber, Config.Storge[i].pos.x, Config.Storge[i].pos.y, Config.Storge[i].pos.z + 0.2, 0.0, 0.0, 0.0, 0, 0.0, 0.0, 0.5, 0.5, 0.5, 0, 255, 0, 150, false, true, 2, false, false, false, false)
					DrawMarker(25, Config.Storge[i].pos.x, Config.Storge[i].pos.y, Config.Storge[i].pos.z - 1.0, 0.0, 0.0, 0.0, 0, 0.0, 0.0, 1.0, 1.0, 1.0, 0, 255, 0, 150, false, true, 2, false, false, false, false)
					if 	raduis <= 1.2 then
						ESX.ShowHelpNotification('<font face="sharlock">~y~E ~w~ﻡﺎﻌﻟﺍ ﻉﺩﻮﺘﺴﻤﻟﺍ ﻦﻣ ﺐﻠﻄﻠﻟ')
						if IsControlJustReleased(0, Keys['E']) then 
							ESX.TriggerServerCallback('esx_shops2:GetOwnShopNumber', function(data)
								exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
								Citizen.SetTimeout(1000, function()
									if data.owneroremps then
										OpenBoss2(data.number, data.owner)
									else
										ESX.ShowNotification('<font color=red>الطلب من المستودع العام لأصحاب المتاجر والموظفين فقط</font>')
									end
								end)
							end)
						end
					end
				end	
			end

			if letsleep then
				Citizen.Wait(500)
			end
			Citizen.Wait(2)
		end
end)

function OpenBoss2(number2, isowner)
	ESX.UI.Menu.CloseAll() 
	ESX.TriggerServerCallback('esx_kr_shop:getOwnedShop', function(data)
	
	  local elements = {}
  
		  table.insert(elements, {label = '<font color=#999999> رصيد المتجر : <font color=#00EE4F>$<font color=#ffffff>' .. data[1].money ,    value = ''})
		 -- table.insert(elements, {label = 'اضافة سلعة للبيع', value = 'putitem'})
		 -- table.insert(elements, {label = 'سحب سلعة من المتجر',    value = 'takeitem'})
		 table.insert(elements, {label = '<font color=orange>ايداع</font> نقود في رصيد المتجر',    value = 'putmoney'})
		 table.insert(elements, {label = 'طلب منتجات جديدة', value = 'buy'})
		 table.insert(elements, {label = '<font color=#1B76F9> تتبع الطلبات ', value = 'shipments'})
		-- table.insert(elements, {label = '<font color=orange>سحب</font> نقود من رصيد المتجر',    value = 'takemoney'})
		 -- table.insert(elements, {label = '<font color=#F98A1B>تغيير اسم المتجر مقابل : <font color=#00EE4F>$<font color=#ffffff>' .. Config.ChangeNamePrice,    value = 'changename'})
		 -- table.insert(elements, {label = '<font color=#CB120D>بيع متجرك مقابل : <font color=#00EE4F>$<font color=#ffffff>' .. math.floor(data[1].ShopValue / Config.SellValue),   value = 'sell'})
  
		  ESX.UI.Menu.Open(
		  'default', GetCurrentResourceName(), 'boss',
		  {
			  title    = 'إدارة متجر <font color=gray>'..data[1].ShopName..'</font>',
			  align    = 'bottom-right',
			  elements = elements
		  },
		  function(data, menu)
		  if data.current.value == 'putitem' then
			  PutItem(number2)
		  elseif data.current.value == 'takeitem' then  
			  TakeItem(number2)
		  elseif data.current.value == 'takemoney' then
			  
  
			  ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'takeoutmoney', {
				  title = 'كم تريد أن تسحب'
			  }, function(data2, menu2)
	
			  local amount = tonumber(data2.value)
			  
			  TriggerServerEvent('esx_kr_shops:takeOutMoney', amount, number2, securityToken)
			  
			  menu2.close()
		  
		  end,
		  function(data2, menu2)
		  menu2.close()
		  end)
  
		   elseif data.current.value == 'putmoney' then
			  ESX.UI.Menu.CloseAll()
  
			  ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'putinmoney', {
			  title = 'كم تريد أن تودع؟'
			  }, function(data3, menu3)
			  local amount = tonumber(data3.value)
			  TriggerServerEvent('esx_kr_shops:addMoney', amount, number2)
			  menu3.close()
				  end,
				  function(data3, menu3)
			  menu3.close()
		  end)
  
		  elseif data.current.value == 'sell' then
			ESX.UI.Menu.CloseAll()    
  
			ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'sell', {
			  title = 'اكتب: ( نعم ) بدون أقواس للتأكيد'
			}, function(data4, menu4)
			  
			  if data4.value == 'نعم' then
				TriggerServerEvent('esx_kr_shops:SellShop', number2, securityToken)
				menu4.close()
			  end
				  end,
				  function(data4, menu4)
			  menu4.close()
		  end)
  
		elseif data.current.value == 'changename' then
		  ESX.UI.Menu.CloseAll()    
  
		  ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'changename', {
			title = 'ماذا تريد تسمية متجرك؟'
		  }, function(data5, menu5)
			  
			  TriggerServerEvent('esx_kr_shops:changeName', number2, data5.value)
			  menu5.close()
						 end,
					  function(data5, menu5)
					  menu5.close()
				  end)
				  
				  ----------------------------------
			  elseif data.current.value == 'shipments' then
				ESX.UI.Menu.CloseAll()
				GetAllShipments(number2)

			elseif data.current.value == 'buy' then
				ESX.UI.Menu.CloseAll()
				OpenShipmentDelivery(number2)
				  end
				  end,
				  function(data, menu)
			  menu.close()
		  end)
	  end, number2)
  end


--Mazad
local ShopLabell = {
	['market'] = '<font color=0059BE>متجر</font>',
	['bar'] = '<font color=7C00BE>بار</font>',
	['pharmacie'] = '<font color=F53030>صيدلية</font>',
	['rts'] = '<font color=f9a825>المطاعم</font>',
	['weapons'] = '<font color=EC7710>محل أسلحة</font>',
	['SodaMachine'] = '<font color=ECA71B>براد</font>',
}
local ShopLabell2 = {
	['market'] = 'متجر',
	['bar'] = 'بار',
	['pharmacie'] = 'صيدلية',
	['rts'] = 'مطعم',
	['weapons'] = 'محل أسلحة',
	['SodaMachine'] = 'براد',
}

RegisterNetEvent('set:NewTable')
AddEventHandler('set:NewTable', function(name_t, remove_me)
	for k,v in pairs(name_t) do
		print(v)
	end
	--table.insert(name_new_the_s, name_t)
end)

RegisterNetEvent('set:New2')
AddEventHandler('set:New2', function()
	if name_new_the == nil then
		local ss = nil
	else
		table.insert(name_new_the_s, {name_player_f = name_new_the, money = money_lastet, number = data_number_store})
	end
end)

RegisterNetEvent('set:New')
AddEventHandler('set:New', function(name_new, money_new, number_the_store)
	name_new_the = name_new
	money_lastet = money_new
	data_number_store = number_the_store
	if name_new_the == nil then
		local sdkjvnjkdsv = nil
	else
		if name_new_the_s ~= nil then
			for k1212,v1212 in pairs(name_new_the_s) do
				local lb1212 = v1212.name_player_f
				local NumberShope1212 = v1212.number
				if lb1212 == name_new_the and data_number_store == NumberShope1212 then
					table.remove(name_new_the_s, k1212)
				end
			end
		end
	end
	table.insert(iiis, {name_n = name_new_the, number_the_shop = data_number_store})
end)

function OpenShopCenter()
	ESX.UI.Menu.CloseAll()
  	local elements = {}
	local alr8m = nil
	local alfah = nil
	ESX.TriggerServerCallback('esx_kr_shop:getShopList', function(ress)
		for i=1, #ress, 1 do	
			for k,v in pairs(Config.Zones) do
				if Config.Zones[k].Pos.number == ress[i].ShopNumber then
					alfah = ShopLabell2[Config.Zones[k].Type]
					alr8m = ress[i].ShopNumber
					table.insert(elements, {label = '<font color=gray>الفئة : ' .. ShopLabell2[Config.Zones[k].Type].. ' | رقم ال'..ShopLabell2[Config.Zones[k].Type]..' : '.. ress[i].ShopNumber ..' | السعر : $'..ESX.Math.GroupDigits(ress[i].ShopValue)..'</font>', value = '3rd', number = ress[i].ShopNumber, type = Config.Zones[k].Type, data = ress[i]})
				end
			end
		end

			ESX.UI.Menu.Open( 'default', GetCurrentResourceName(), 'mazad',
            {
                title    = 'مزاد المتاجر',
                align = 'top-left', 
                elements = elements
            },
            function(data, menu) 
                local action = data.current.value

				if action == '3rd' then
					if PlayerData.job.name == 'admin' then
						--------------------------------------
						--------------------------------------
						--------------------------------------
						
						ESX.TriggerServerCallback('esx_shops2:checkmazadstartornot', function(resssss) 
							local elements5 = {}
							if name_new_the_s ~= nil then
								for k,v in pairs(name_new_the_s) do
									local lb = v.name_player_f
									local moneY_add = v.money
									local NumberShope = v.number
									if v.name_player_f == name_new_the then
										local sdpovsd = nil
									elseif data.current.number == NumberShope then
										table.insert(elements5, {label = '<font color=red>المزايدين | ' .. lb .. " | $" .. moneY_add .. '</font>', value = ''})
									end
								end
							end
							if resssss.done then
								table.insert(elements5, {label = '<font color=green>عرض</font>', value = '3rd2'})
								table.insert(elements5, {label = '<font color=red>رجوع</font>', value = 'cancel'})
							else
								if name_new_the == nil then
									table.insert(elements5, {label = '<font color=yellow>سعر الشراء | ' .. "لايوجد احد مزايد" .. ' | $'..ESX.Math.GroupDigits(resssss.data.money)..'</font>', value = ''})
								end
								for kk,vs in pairs(iiis) do
									if data.current.number == vs.number_the_shop and name_new_the == vs.name_n then
										table.insert(elements5, {label = '<font color=yellow>سعر الشراء | ' .. name_new_the .. ' | $'..ESX.Math.GroupDigits(resssss.data.money)..'</font>', value = ''})
										break
									end
								end
								table.insert(elements5, {label = '<font color=green>شراء</font>', value = '3rd3222222'})
								table.insert(elements5, {label = 'إنهاء البيع', value = '3rd32'})
								table.insert(elements5, {label = '<font color=red>إلغاء البيع</font>', value = '3rd3'})
							end
							ESX.UI.Menu.Open( 'default', GetCurrentResourceName(), 'mazad2',
								{
									title    = 'التحكم بال'..ShopLabell2[data.current.type]..' رقم '..data.current.number,
									align = 'top-left', 
									elements = elements5
								},
								function(data2, menu2)
									local action2 = data2.current.value

									if action2 == '3rd2' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell2[data.current.type], 0, 'add', securityToken, ShopLabell2[data.current.type], ShopLabell2[data.current.type], data.current.number)
										menu2.close()
									elseif action2 == 'cancel' then
										menu2.close()
									elseif action2 == '3rd3' then
										for k11,v11 in pairs(name_new_the_s) do
											local NumberShope2 = v11.number
											if data.current.number == NumberShope2 then
												table.remove(name_new_the_s, k11)
											end
										end
										for k12,v12 in pairs(iiis) do
											local NumberShope3 = v12.number_the_shop
											if data.current.number == NumberShope3 then
												table.remove(iiis, k12)
											end
										end
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell2[data.current.type], 0, 'remove', securityToken)
										menu2.close()
									elseif action2 == '3rd32' then
										for k11,v11 in pairs(name_new_the_s) do
											local NumberShope2 = v11.number
											if data.current.number == NumberShope2 then
												table.remove(name_new_the_s, k11)
											end
										end
										for k12,v12 in pairs(iiis) do
											local NumberShope3 = v12.number_the_shop
											if data.current.number == NumberShope3 then
												table.remove(iiis, k12)
											end
										end
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell2[data.current.type], 0, 'close', securityToken, ShopLabell2[data.current.type], ShopLabell2[data.current.type], data.current.number, ESX.Math.GroupDigits(resssss.data.money))
										menu2.close()
									elseif action2 == '3rd3222222' then
										ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'SSSSSSSSSS', {
											title = 'المزايدة من $'..ESX.Math.GroupDigits(Config.Mazad.L)..' إلى $'..ESX.Math.GroupDigits(Config.Mazad.H)
										}, function(data3, menu3)
							  
											local amountttttt = tonumber(data3.value)
										
											TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell2[data.current.type], amountttttt, 'playermazad', securityToken)
											menu3.close()
										end, function(data3, menu3)
											menu3.close()
										end)
									end

								end, function(data2, menu2)
									menu2.close()
								end)
						end, data.current.number)
					else

						ESX.TriggerServerCallback('esx_shops2:checkmazadstartornot', function(resssss) 
							local elements5 = {}
							if name_new_the_s ~= nil then
								for k,v in pairs(name_new_the_s) do
									local lb = v.name_player_f
									local moneY_add = v.money
									local NumberShope = v.number
									if v.name_player_f == name_new_the then
										local sdpovsd = nil
									elseif data.current.number == NumberShope then
										table.insert(elements5, {label = '<font color=red>المزايدين | ' .. lb .. " | $" .. moneY_add .. '</font>', value = ''})
										break
									end
								end
							end
							if resssss.done then
								table.insert(elements5, {label = '<font color=gray>لا يوجد مزاد على هذا ال'..ShopLabell2[data.current.type]..'</font>', value = ''})
							else
								if name_new_the == nil then
									table.insert(elements5, {label = '<font color=yellow>سعر الشراء | ' .. "لايوجد احد مزايد" .. ' | $'..ESX.Math.GroupDigits(resssss.data.money)..'</font>', value = ''})
								end
								for kk,vs in pairs(iiis) do
									if data.current.number == vs.number_the_shop and name_new_the == vs.name_n then
										table.insert(elements5, {label = '<font color=yellow> سعر الشراء | ' .. name_new_the .. ' | $'..ESX.Math.GroupDigits(resssss.data.money)..'</font>', value = ''})
										break
									end
								end
								table.insert(elements5, {label = '<font color=green>المزايدة</font>', value = '3rd3222222'})
							end
							ESX.UI.Menu.Open( 'default', GetCurrentResourceName(), 'mazad2',
								{
									title    = 'التحكم بال'..ShopLabell2[data.current.type]..' رقم '..data.current.number,
									align = 'top-left', 
									elements = elements5
								},
								function(data2, menu2)
									local action2 = data2.current.value

									if action2 == '3rd2' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell2[data.current.type], 0, 'add', securityToken)
										menu2.close()
									elseif action2 == 'cancel' then
										menu2.close()
									elseif action2 == '3rd3' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell2[data.current.type], 0, 'remove', securityToken)
										menu2.close()
									elseif action2 == '3rd32' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell2[data.current.type], 0, 'close', securityToken)
										menu2.close()
									elseif action2 == '3rd3222222' then
										ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'SSSSSSSSSS', {
											title = 'المزايدة من $'..ESX.Math.GroupDigits(Config.Mazad.L)..' إلى $'..ESX.Math.GroupDigits(Config.Mazad.H)
										}, function(data3, menu3)
							  
											local amountttttt = tonumber(data3.value)
										
											TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell2[data.current.type], amountttttt, 'playermazad', securityToken)
											menu3.close()
										end, function(data3, menu3)
											menu3.close()
										end)
									end

								end, function(data2, menu2)
									menu2.close()
								end)
						end, data.current.number)

						--------------------------------------
						--------------------------------------
						--------------------------------------
					end
				end

            end, function(data, menu) 
                menu.close()
            end)
	end)
end

---------------------
-- WEAPONS - CRAFT --
---------------------

Citizen.CreateThread( function()
	while true do
		Citizen.Wait(1)
		if BLOCKINPUTCONTROL then
			DisableControlAction(0, 24, true) -- Attack
			DisableControlAction(0, 257, true) -- Attack 2
			DisableControlAction(0, 25, true) -- Aim
			DisableControlAction(0, 263, true) -- Melee Attack 1
			DisableControlAction(0, Keys['W'], true) -- W
			DisableControlAction(0, Keys['A'], true) -- A
			DisableControlAction(0, Keys['E'], true) -- A
			DisableControlAction(0, 31, true) -- S (fault in Keys table!)
			DisableControlAction(0, 30, true) -- D (fault in Keys table!)

			DisableControlAction(0, Keys['R'], true) -- Reload
			DisableControlAction(0, Keys['SPACE'], true) -- Jump
			DisableControlAction(0, Keys['Q'], true) -- Cover
			DisableControlAction(0, Keys['TAB'], true) -- Select Weapon
			DisableControlAction(0, Keys['F'], true) -- Also 'enter'?

			DisableControlAction(0, Keys['F1'], true) -- Disable phone
			DisableControlAction(0, Keys['F2'], true) -- Inventory
			DisableControlAction(0, Keys['F3'], true) -- Animations
			DisableControlAction(0, Keys['F6'], true) -- Job

			DisableControlAction(0, Keys['V'], true) -- Disable changing view
			DisableControlAction(0, Keys['C'], true) -- Disable looking behind
			DisableControlAction(0, Keys['X'], true) -- Disable clearing animation
			DisableControlAction(2, Keys['P'], true) -- Disable pause screen

			DisableControlAction(0, 59, true) -- Disable steering in vehicle
			DisableControlAction(0, 71, true) -- Disable driving forward in vehicle
			DisableControlAction(0, 72, true) -- Disable reversing in vehicle

			DisableControlAction(2, Keys['LEFTCTRL'], true) -- Disable going stealth

			DisableControlAction(0, 47, true)  -- Disable weapon
			DisableControlAction(0, 264, true) -- Disable melee
			DisableControlAction(0, 257, true) -- Disable melee
			DisableControlAction(0, 140, true) -- Disable melee
			DisableControlAction(0, 141, true) -- Disable melee
			DisableControlAction(0, 142, true) -- Disable melee
			DisableControlAction(0, 143, true) -- Disable melee
			DisableControlAction(0, 75, true)  -- Disable exit vehicle
			DisableControlAction(27, 75, true) -- Disable exit vehicle
		end
	end
end)


function CraftingWeapons()
	ESX.TriggerServerCallback('esx_shops2:CraftWeap9923ons2', function(okk2)
		if okk2 then
			ESX.UI.Menu.Open( 'default', GetCurrentResourceName(), 'CRAFT',
			{
				title    = 'قائمة تصنيع سلاح',
				align = 'top-left', 
				elements = {
					{ label = 'رشاش مايكرو', value = 'WEAPON_MICROSMG_box' },
					{ label = 'شوزن', value = 'WEAPON_PUMPSHOTGUN_box' },
				}
			},
			function(data2, menu2)
					ESX.TriggerServerCallback('esx_shops2:CraftWeap9923ons', function(okk)
						if okk then
							BLOCKINPUTCONTROL = true
							local msgg = 'تتم الآن عملية تصنيع سلاح</br><font size=4>النوع: '..data2.current.label..'</br><font color=orange>الرجاء الإنتظار</font></font>'
							TriggerEvent("pNotify:SendNotification", {
								text = "<font size=5 color=white><center><b>"..msgg,
								type = 'success',
								queue = left,
								timeout = Config.WeaponCraftTime,
								killer = false,
								theme = "gta",
								layout = "CenterLeft",
							})
							ESX.UI.Menu.CloseAll()
							Citizen.Wait(Config.WeaponCraftTime)
							BLOCKINPUTCONTROL = false
							ESX.ShowNotification('<font color=green>تم تصنيع السلاح بنجاح</font>')
						end
					end, data2.current.value)
			end, function(data2, menu2)
				menu2.close()
			end)
		else
			ESX.ShowNotification('<font color=red>تصنيع السلاح متاح لمالك متجر الأسلحة والموظفين</font>')
		end
	end)
end

-- Create blips
Citizen.CreateThread(function(); while not configready do; Citizen.Wait(1000); end
	local pPSOS = Config.Zones.crafting.Pos
	local blip = AddBlipForCoord(pPSOS.x, pPSOS.y, pPSOS.z)

	SetBlipSprite (blip, 110)
	SetBlipDisplay(blip, 4)
	SetBlipScale  (blip, 1.0)
	SetBlipColour (blip, 1)
	SetBlipAsShortRange(blip, true)

	BeginTextCommandSetBlipName("STRING")
	AddTextComponentSubstringPlayerName('<FONT FACE="sharlock">ﺡﻼﺳ ﻊﻴﻨﺼﺗ')
	EndTextCommandSetBlipName(blip)
end)

local blipRobbery = nil

function setblip_robbery(position)
	blipRobbery = AddBlipForCoord(position.x, position.y, position.z)
	SetBlipSprite(blipRobbery , 161)
	SetBlipScale(blipRobbery , 2.0)
	SetBlipColour(blipRobbery, 3)
	PulseBlip(blipRobbery)
end

RegisterNetEvent('esx_shops2:RobberyStartLeoJob')
AddEventHandler('esx_shops2:RobberyStartLeoJob', function(type, position)
	if type == 'start' then
		setblip_robbery(position)
	elseif type == 'stop' then
		RemoveBlip(blipRobbery)
	end
end)