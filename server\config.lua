Config                            = {}
Config.DrawDistance               = 20.0
Config.Locale = 'en'
Config.DeliveryTime = 0 -- IN SECOUNDS DEFAULT (18000) IS 5 HOURS / 300 MINUTES
Config.TimeBetweenRobberies = 0.5 -- 3 hours
Config.CutOnRobbery = 10 -- IN PERCENTAGE FROM THE TARGET SHOP
Config.RequiredPolices = 0 -- 4
Config.SellValue = 2 -- This is the shops value divided by 2
Config.ChangeNamePrice = 0 -- In $ - how much you can change the shops name for

Config.WeaponCraftTime = 0 * 0

Config.BoxMax = {
  ['market'] = 40,
  ['bar'] = 15,
  ['pharmacie'] = 15,
  ['rts'] = 15,
  ['weapons'] = 15,
  ['SodaMachine'] = 10,
}

Config.addXpForBoxDelivery = 100

Config.Mazad = {
  xp = 35,
  H = 500000, -- حد اعلى
  L = 100000, -- حد ادنى
}
Config.is_set_duple = false
Config.Items = {
  ['market'] = { -- 30 متجر
    [1] = {label = "صندوق مياه معدنية", item = "waterbox", price = 500, itemConvert = 'water', count = 20, max = 1000},
    [2] = {label = "صندوق برجر", item = "breadbox", price = 500, itemConvert = 'bread', count = 20, max = 1000},
    [3] = {label = "صندوق شوكلاتة", item = "chocolatebox", price = 500, itemConvert = 'chocolate', count =  30, max = 2000},
    [4] = {label = "صندوق كولا", item = "cocacolabox", price = 500, itemConvert = 'cocacola', count = 15, max = 2000},
    [5] = {label = "صندوق فطيرة", item = "cupcakebox", price = 700, itemConvert = 'cupcake', count = 25, max = 2000},
    [6] = {label = 'صندوق كاشف رادار', item = "speedcamera_detectorbox", price = 35000, itemConvert = 'speedcamera_detector', count = 10, max = 500},
    [7] = {label = 'صندوق دريل', item = "drill", price = 40000, itemConvert = 'drill', count = 4, max = 200},
    [8] = {label = 'صندوق قناع أكسجين', item = "oxygen_mask", price = 800, itemConvert = 'oxygen_mask', count = 10, max = 500},
    [22] = {label = 'صندوق طعام حيوانات أليفة', item = "croquettes_box", price = 1000, itemConvert = 'croquettes', count = 10, max = 500},
    [9] =  {label = 'صندوق معادن', item = "minerToolbox", price = 2500, itemConvert = 'm_tool', count = 7, max = 1500},
    [10] = {label = 'صندوق دواجن', item = "slaughtererToolbox", price = 600, itemConvert = 's_tool', count = 7, max = 1500},
    [11] = {label = 'صندوق غاز', item = "f_tool", price = 3500, itemConvert = 'f_tool', count = 50, max = 500},
    [12] = {label = 'صندوق أخشاب', item = "lumberjackToolbox", price = 650, itemConvert = 'l_tool', count = 20, max = 1500},
    [13] = {label = 'صندوق خضروات', item = "boxv_tool", price = 500, itemConvert = 'v_tool', count = 20, max = 1500},
    [14] = {label = 'صندوق أقمشة', item = "tailorToolbox", price = 500, itemConvert = 't_tool', count = 10, max = 1500},
   -- [14] = {label = 'صندوق عدة أسمنت', item = "cement_kit_box", price = 1200, itemConvert = 'cement_kit', count = 10, max = 1500},
    [15] = {label = 'صندوق خيشة', item = "headbagbox", price = 1500, itemConvert = 'headbag', count = 4, max = 200},
    [16] = {label = 'صندوق سنارة صيد سمك', item = "fishbaitboxX", price = 3500, itemConvert = 'fishingrod', count = 10, max = 500},
    [17] = {label = 'صندوق طعم سلاحف بحرية', item = "turtlebaitbox", price = 2000, itemConvert = 'turtlebait', count = 20, max = 500},
    [18] = {label = 'صندوق طعم اسماك', item = "fishbaitbox", price = 1200, itemConvert = 'fishbait', count = 25, max = 500},
    [19] = {label = 'صندوق لاب توب', item = "laptop_h", price = 160000, itemConvert = 'laptop_h', count = 4, max = 200},
    --[17] = {label = 'صندوق شحنة حرارية', item = "thermal_charge_box", price = 15000, itemConvert = 'thermal_charge', count = 4, max = 100},
    [20] = {label = 'صندوق راديو', item = "radiobox", price = 40000, itemConvert = 'radio', count = 4, max = 50},
   -- [19] = {label = "عدة سمكرة", item = "carokit", itemConvert = 'carokit', count = 1, max = 500, Storge = false},
   [21] = {label = 'صندوق تصليح', item = "fixkit", price = 700, itemConvert = 'fixkit', count = 4, max = 150},
   [22] = {label = "صندوق سجائر", item = "cigarette_box", price = 150, itemConvert = 'cigarette', count = 20, max = 500},
   [23] = {label = "صندوق ولاعات", item = "lighter_box", price = 300, itemConvert = 'lighter', count = 20, max = 500},
   [24] = {label = "كرتون شيبس", item = "chepsbox", price = 400, itemConvert = 'cheps', count = 25, max = 2000},
   [25] = {label = "صندوق جوالات", item = "phone_box", price = 8200, itemConvert = 'phone', count = 4, max = 5},
   [26] = {label = 'صندوق عدة أسمنت', item = "brickworkkit", price = 1200, itemConvert = 'brickworkkit', count = 20, max = 500},
   [27] = {label = 'صندوق عدة مياه', item = "waterrerToolbox", price = 2500, itemConvert = 'wa_tool', count = 20, max = 1500},
   [28] = {label = 'صندوق عدة تمور', item = "tomoterToolbox", price = 2000, itemConvert = 'tomot_tool', count = 20, max = 1500},
   [29] = {label = 'صندوق عدة حليب', item = "milkerToolbox", price = 2200, itemConvert = 'milk_tool', count = 20, max = 1500},
  },
  ['bar'] = { -- 4 بارات
    [1] = {label = "صندوق مياه معدنية", item = "waterbox", price = 500, itemConvert = 'water', count = 20, max = 1000},
   -- [2] = {label = "صندوق برجر", item = "breadbox", price = 150, itemConvert = 'bread', count = 20, max = 1000},
    [2] = {label = "صندوق سجائر", item = "cigarette_box", price = 150, itemConvert = 'cigarette', count = 20, max = 500},
   -- [4] = {label = "صندوق ولاعات", item = "lighter_box", price = 300, itemConvert = 'lighter', count = 20, max = 500},
    [3] = {label = "خمر", item = "beer", itemConvert = 'beer', count = 1, max = 500, Storge = false},
    [4] = {label = "خمر فاخر", item = "grand_cru", itemConvert = 'grand_cru', count = 1, max = 500, Storge = false},
    [5] = {label = "عنب", item = "grape", itemConvert = 'grape', count = 1, max = 1000, Storge = false},
    [6] = {label = "عصير عنب", item = "grape_juice", itemConvert = 'grape_juice', count = 1, max = 1000, Storge = false},
  },
  ['pharmacie'] = { -- 3 صيدليات
    [1] = {label = "صندوق دواء زانكس", item = "xanax_box", price = 3000, itemConvert = 'xanax', count = 25, max = 500},
    [2] = {label = "صندوق ضمادات جروح", item = "bandage_box", price = 2800, itemConvert = 'bandage', count = 15, max = 500},
    [3] = {label = "صندوق إسعافات أولية", item = "medikit_box", price = 15000, itemConvert = 'medikit', count = 15, max = 1000},
  },
  ['rts'] = { -- 3 مطاعم
    [1] = {label = "صندوق مياه معدنية", item = "waterbox", price = 500, itemConvert = 'water', count = 20, max = 1000},
    [2] = {label = "صندوق برجر", item = "breadbox", price = 500, itemConvert = 'bread', count = 20, max = 1000},
    [3] = {label = "صندوق كولا", item = "cocacolabox", price = 500, itemConvert = 'cocacola', count = 15, max = 2000},
    [4] = {label = "صندوق فطيرة", item = "cupcakebox", price = 700, itemConvert = 'cupcake', count = 25, max = 2000},
    [5] = {label = "أكياس بطاطس", item = "batatobox", price = 900, itemConvert = 'batato', count = 25, max = 2000},
    [6] = {label = "كرتون بيبسي", item = "pepsibox", price = 1200, itemConvert = 'pepsi', count = 16, max = 2000},
    [7] = {label = "كرتون سوشي", item = "coshebox", price = 2000, itemConvert = 'coshe', count = 10, max = 2000},
    [8] = {label = "صندوق برجر وسط", item = "bergrulbox", price = 6000, itemConvert = 'bergrul', count = 15, max = 2000},
    [9] = {label = "صندوق وجبة برجر كبير", item = "bergrkbbox", price = 8000, itemConvert = 'bergrkb', count = 15, max = 2000},
  },
  ['weapons'] = { -- 4 محلات اسلحة
    [1] = {label = 'صندوق سترة مضادة للرصاص', item = "bulletproof_box", price = 8000, itemConvert = 'bulletproof', count = 4, max = 200},
    [2] = {label = 'صندوق تعبئة طلقات', item = "boxbig_box", price = 4000, itemConvert = 'boxbig', count = 8, max = 200},
    [3] = {label = 'صندوق منظار يدوي', item = "binoculars_box", price = 20000, itemConvert = 'binoculars', count = 2, max = 100},
    [4] = {label = 'صندوق أدوات أسلحة', item = "weakit_box", price = 72000, itemConvert = 'weakit', count = 6, max = 100},

    [5] = {label = 'عدة تصنيع سلاح', item = "weaponcrafting", price = 160000, itemConvert = 'weaponcrafting', count = 1, instore = false}, -- for crafting

    --weapons
    [6] = {label = 'صندوق مسدس', item = "WEAPON_PISTOL_box", price = 20000, itemConvert = 'WEAPON_PISTOL', type = 'weapon', info = { price = 15000, xp = 10, lisence = "weapon"}, count = 2, max = 100},
    [7] = {label = 'صندوق كشاف', item = "WEAPON_FLASHLIGHT_box", price = 8000, itemConvert = 'WEAPON_FLASHLIGHT', type = 'weapon', info = { price = 3000, xp = 1, lisence = "weapon" }, count = 8, max = 100},
    
    [8] = {label = 'صندوق ساطور', item = "WEAPON_MACHETE_box", price = 12000, itemConvert = 'WEAPON_MACHETE', count = 6, type = 'weapon', info = { price = 5000, xp = 5 , lisence = "weapon" }, black = true, max = 50},
    [9] = {label = 'صندوق سكين', item = "WEAPON_SWITCHBLADE_box", price = 6000, itemConvert = 'WEAPON_SWITCHBLADE', count = 6, type = 'weapon', info = { price = 4000, xp = 5, lisence = "weapon" }, black = true, max = 50},
    [10] = {label = 'صندوق فأس', item = "WEAPON_BATTLEAXE_box", price = 12000, itemConvert = 'WEAPON_BATTLEAXE', count = 6, type = 'weapon', info = { price = 5000, xp = 5,  lisence = "weapon" }, black = true, max = 50},
    --crafting
    [11] = {label = 'صندوق شوزن', item = 'WEAPON_PUMPSHOTGUN_box', itemConvert = 'WEAPON_PUMPSHOTGUN', count = 2, Storge = false, type = 'weapon', info = { price = 175000, xp = 25, lisence = "weapon"}, black = true, max = 25},
    [12] = {label = 'صندوق مايكرو', item = 'WEAPON_MICROSMG_box', itemConvert = 'WEAPON_MICROSMG', count = 2, Storge = false, type = 'weapon', info = { price = 150000, xp = 40, lisence = "weapon" }, black = true, max = 25},
    [13] = {label = 'جهاز تهكير البنك المركزي', item = 'id_card_box', itemConvert = 'id_card', count = 2, max = 25},
    [14] = {label = 'جهاز تهكير بنك صغير', item = 'id_card_f_box', itemConvert = 'id_card_f', count = 2, max = 25},
    [15] = {label = 'لابتوب', item = 'laptop_h_box', itemConvert = 'laptop_h', count = 2, max = 25},
    [16] = {label = 'شحنات حرارية', item = 'thermal_charge_box', itemConvert = 'thermal_charge', count = 2, max = 25},
    [17] = {label = 'حبل اسود', item = 'winch', itemConvert = 'winch', count = 2, max = 25},
    [18] = {label = 'دريل ليزر', item = 'laser_drill', itemConvert = 'laser_drill', count = 2, max = 25},
  },
  ['SodaMachine'] = { -- 15 براد
    [1] = {label = "صندوق مياه معدنية", item = "waterbox", price = 500, itemConvert = 'water', count = 20, max = 1000},
    [3] = {label = "صندوق شوكلاتة", item = "chocolatebox", price = 500, itemConvert = 'chocolate', count =  30, max = 2000},
    [4] = {label = "صندوق كولا", item = "cocacolabox", price = 500, itemConvert = 'cocacola', count = 15, max = 2000},
    [5] = {label = "صندوق فطيرة", item = "cupcakebox", price = 700, itemConvert = 'cupcake', count = 25, max = 2000},
    [6] = {label = "كرتون شيبس", item = "chepsbox", price = 400, itemConvert = 'cheps', count = 25, max = 2000},
  }
}

Config.Storge = {
  [1] = {
    pos = {x = 765.27429199219, y = -3202.6884765625, z = 6.0137372016907 }
  },
  [2] = {
    pos = {x = 765.14129638672, y = -3187.5368652344, z = 6.0254745483398}
  },
  [3] = {
    pos = {x = 819.32800292969, y = -3194.39453125, z = 5.900815486908 }
  },
  [4] = {
    pos = {x = 827.39227294922, y = -3208.3137207031, z = 5.9008197784424}
  },
  [5] = {
    pos = {x = 834.77355957031, y = -3207.8352050781, z = 5.9008164405823}
  },
  [6] = {
    pos = {x = 849.1083984375, y = -3207.359375, z = 5.9007439613342}
  },
  [7] = {
    pos = {x = 854.970703125, y = -3206.9436035156, z = 5.9007487297058}
  },
  [8] = {
    pos = {x = 865.91473388672, y = -3206.6291503906, z = 5.9006609916687}
  },
}

Config.Storge2 = {
  [1] = {
    pos = {x = -127.93429199219, y = -2535.0184765625, z = 6.0037372016907 }
  },
  [2] = {
    pos = {x = -110.000703125, y = -2509.8136035156, z = 4.8507487297058}
  },
  [3] = {
    pos = {x = -106.070703125, y = -2503.7936035156, z = 4.8507487297058}
  },
  [4] = {
    pos = {x = -122.830703125, y = -2491.9436035156, z = 6.0107487297058}
  },
  [5] = {
    pos = {x = -126.980703125, y = -2497.6536035156, z = 6.0107487297058}
  },
  [6] = {
    pos = {x = -135.220703125, y = -2509.7136035156, z = 6.107487297058}
  },
  [7] = {
    pos = {x = -138.540703125, y = -2514.6636035156, z = 6.1007487297058}
  },
  [8] = {
    pos = {x = -144.90703125, y = -2523.2236035156, z = 6.0007487297058}
  },
}

Config.Zones = {
  ShopCenter = {
    Pos   = {x = 682.0399780273438,   y = 565.530029296875,  z = 129.0500030517578 - 1.0, number = 'center'},
  },

  crafting = {
    Pos   = {x = 802.60229492188, y = -88.324851989746, z = 74.92000579834 - 1.0, number = 'crafting', craft = true},
  },

  --BAR
  [1] = {Type = 'bar', Pos = { x = -774.4271, y = 5603.1284, z =  33.7408, - 1.0, number = 1}, Object = { x = -114.68257141113, y = 6384.7504882812, z = 32.180126190186 - 1.0, h = 76.57}},
  [101] = {Type = 'bar', Pos = { x = -771.7433,  y = 5602.5732, z = 33.7408, - 1.0, number = 101 , red = true}},

  [2] = {Type = 'bar', Pos = { x = 1985.5075, y = 3052.7014, z = 47.21 - 1.0, number = 2}, Object = { x = 1993.3206787109, y = 3050.5913085938, z = 47.215274810791 - 1.0, h = 4.86}},
  [102] = {Type = 'bar', Pos = { x = 1990.0175, y = 3047.8984, z = 47.2151, - 1.0, number = 102 , red = true}},
  
  [3] = {Type = 'bar', Pos = { x = -1393.7664794922, y = -606.80535888672, z = 30.319547653198 - 1.0, number = 3}, Object = { x = -1382.2415771484, y = -632.43048095703, z = 30.819561004639 - 1.0, h = 222.47}},
  [103] = {Type = 'bar', Pos = { x = -1383.6768798828, y = -629.28344726562, z = 30.819566726685 - 1.0, number = 103 , red = true}},

  --WEAPONS
  [4] = {Type = 'weapons', Pos = { x = 6.6504, y = -1105.2454, z = 29.1176 - 1.0, number = 4}, Object = { x = 29.5025, y = -1079.5205, z = 27.7232 - 1.0, h = 93.14}},
  [104] = {Type = 'weapons', Pos = { x = 33.7389, y = -1077.5146, z = 27.7232 - 1.0, number = 104 , red = true}},

  [5] = {Type = 'weapons', Pos = { x = 1693.6502685547, y = 3759.7641601562, z = 34.705310821533 - 1.0, number = 5}, Object = { x = 1699.5583496094, y = 3755.8117675781, z = 34.705368041992 - 1.0, h = 227.04}},
  [105] = {Type = 'weapons', Pos = { x = 1697.7174072266, y = 3758.1162109375, z = 34.705375671387 - 1.0, number = 105 , red = true}},
  
  [6] = {Type = 'weapons', Pos = { x = -330.28884887695, y = 6083.5375976562, z = 31.454761505127 - 1.0, number = 6}, Object = { x = -324.5280456543, y = 6079.5380859375, z = 31.454759597778 - 1.0, h = 220.97}},
  [106] = {Type = 'weapons', Pos = { x = -326.65054321289, y = 6082.021484375, z = 31.45477104187 - 1.0, number = 106 , red = true}},

  --PHARMACIE
  [7] = {Type = 'pharmacie', Pos = { x = 317.83837890625, y = -1076.7145996094, z = 29.478578567505 - 1.0, number = 7}, Object = { x = 326.73440551758, y = -1078.0108642578, z = 29.481616973877 - 1.0, h = 184.52}},
  [107] = {Type = 'pharmacie', Pos = { x = 326.19342041016, y = -1075.9624023438, z = 29.489372253418 - 1.0, number = 107 , red = true}},

  [8] = {Type = 'pharmacie', Pos = { x = -172.90054321289, y = 6385.78515625, z = 31.495471954346 - 1.0, number = 8}, Object = { x = -173.76063537598, y = 6389.4594726562, z = 31.49561882019 - 1.0, h = 229.28}},
  [108] = {Type = 'pharmacie', Pos = { x = -176.52363586426, y = 6391.5620117188, z = 31.495742797852 - 1.0, number = 108 , red = true}},
  --rts

  --SODAMACHINE
  [9] = {Type = 'SodaMachine', Pos = { x = 408.91928100586, y = -1613.4937744141, z = 29.291561126709 - 1.0, number = 9}},
  [10] = {Type = 'SodaMachine', Pos = { x = 1673.8820800781, y = 3834.626953125, z = 34.898639678955 - 1.0, number = 10}},
  [11] = {Type = 'SodaMachine', Pos = { x = -346.64266967773, y = 6072.1962890625, z = 31.46435546875 - 1.0, number = 11}},
  [12] = {Type = 'SodaMachine', Pos = { x = -563.73876953125, y = 5383.263671875, z = 69.544052124023 - 1.0, number = 12}},
  [13] = {Type = 'SodaMachine', Pos = { x = 815.1201171875, y = -2972.072265625, z = 6.0206570625305 - 1.0, number = 13}},
  [14] = {Type = 'SodaMachine', Pos = { x = 717.84405517578, y = -2553.4470214844, z = 19.814624786377 - 1.0, number = 14}},
  [15] = {Type = 'SodaMachine', Pos = { x = 35.372180938721, y = -2537.4694824219, z = 6.1557121276855 - 1.0, number = 15}},
  [16] = {Type = 'SodaMachine', Pos = { x = 433.52359008789, y = -657.95819091797, z = 28.783494949341 - 1.0, number = 16}},
  [17] = {Type = 'SodaMachine', Pos = { x = 1147.1302490234, y = -1521.2679443359, z = 34.843738555908 - 1.0, number = 17}},
  [18] = {Type = 'SodaMachine', Pos = { x = 1847.9249267578, y = 3677.1435546875, z = 34.272296905518 - 1.0, number = 18}},
  [19] = {Type = 'SodaMachine', Pos = { x = -235.73170471191, y = 6315.9340820312, z = 31.481941223145 - 1.0, number = 19}},
  [20] = {Type = 'SodaMachine', Pos = { x = 417.98495483398, y = -985.43151855469, z = 29.408435821533 - 1.0, number = 20}},
  [21] = {Type = 'SodaMachine', Pos = { x = -444.69448852539, y = 6024.0493164062, z = 31.490100860596 - 1.0, number = 21}},
  [22] = {Type = 'SodaMachine', Pos = { x = 1846.9528808594, y = 2587.4880371094, z = 45.672374725342 - 1.0, number = 22}},

  --Market
  [23] = {Type = 'pharmacie', Pos = { x = -176.4954, y = 6383.4624, z = 31.4954, - 1.0, number = 23}, Object = { x = -173.76063537598, y = 6389.4594726562, z = 31.49561882019 - 1.0, h = 229.28}},
  [123] = {Type = 'pharmacie', Pos = { x = -172.3766, y = 66388.1553, z = 31.4952, - 1.0, number = 123 , red = true}},

  [23] = {Type = 'market', Pos = { x = 239.0334, y = -898.7112, z = 29.6232 - 1.0, number = 23}, Object = {x = 244.4485, y = -905.5144, z = 29.6232 - 1.0, h = 42.69}},
  [123] = {Type = 'market', Pos = {x = 245.8903, y = -902.2595, z = 29.6232 - 1.0, number = 123 , red = true}},

  [24] = {Type = 'market', Pos = { x = -1227.86, y = 6927.144, z = 19.475, number = 24}, Object = {x = -1225.65, y = 6937.203, z = 20.475 - 1.0, h = 42.69}},
  [124] = {Type = 'market', Pos = {x = -1227.93, y = 6934.748, z = 20.475 - 1.0, number = 125 , red = true}},

  [25] = {Type = 'market', Pos = {x = 1961.4779052734, y = 3740.7106933594, z = 32.343734741211 - 1.0, number = 25}, Object = {x = 1960.7133789063, y = 3748.8032226563, z = 32.343734741211 - 1.0, h = 283.32}},
  [125] = {Type = 'market', Pos = {x = 1957.4331054688, y = 3746.9916992188, z = 32.343734741211 - 1.0, number = 125 , red = true}},

  [26] = {Type = 'market', Pos = {x = -1487.4417724609, y = -379.48852539063, z = 40.163387298584 - 1.0, number = 26}, Object = {x = -1479.2185058594, y = -374.1755065918, z = 39.163261413574 - 1.0, h = 219.62}},
  [126] = {Type = 'market', Pos = {x = -1480.2697753906, y = -373.16152954102, z = 39.163249969482 - 1.0, number = 126 , red = true}},

  [27] = {Type = 'market', Pos = { x = 161.48846435547, y = 6640.427734375, z = 31.710620880127 - 1.0, number = 27}, Object = {x = 169.06771850586, y = 6643.439453125, z = 31.710647583008 - 1.0, h = 226.37}},
  [127] = {Type = 'market', Pos = { x = 167.04598999023, y = 6645.6953125, z = 31.710622787476 - 1.0, number = 127 , red = true}},
  
  [28] = {Type = 'market', Pos = { x = -48.471775054932, y = -1757.2221679688, z = 29.421014785767 - 1.0, number = 28}, Object = {x = -42.472969055176, y = -1749.3958740234, z = 29.421016693115 - 1.0, h = 328.88}},
  [128] = {Type = 'market', Pos = { x = -43.516532897949, y = -1750.6414794922, z = 29.421020507813 - 1.0, number = 128 , red = true}},
  
  [40] = {Type = 'market', Pos = {x= 374.6000061035156, y= 326.25, z= 103.56999969482422 - 1.0, number = 40}, Object = {x= 379.61, y= 332.11, z= 103.57 - 1.0, h = 42.69}},
  [140] = {Type = 'market', Pos = {x= 376.3200073242188, y= 333.1099853515625,z=  103.56999969482422 - 1.0, number = 140 , red = true}},

  [41] = {Type = 'market', Pos = {x= -708.06, y= -914.37, z= 19.22 - 1.0, number = 41}, Object = {x = -708.697, y = -904.994, z = 19.215 - 1.0, h = 42.69}},
  [141] = {Type = 'market', Pos = {x = -709.402, y = -906.740, z = 19.215 - 1.0, number = 141 , red = true}},

  [42] = {Type = 'market', Pos = {x= 1163.22, y= -323.94, z= 69.21 - 1.0, number = 42}, Object = {x= 1161.152, y= -313.751, z= 69.205 - 1.0, h = 42.69}},
  [142] = {Type = 'market', Pos = {x= 1161.36, y= -315.83, z= 69.21 - 1.0, number = 142 , red = true}},


  [43] = {Type = 'market', Pos = {x= 1136.20, y= -982.27, z= 46.42 - 1.0, number = 43}, Object = {x= 1126.64, y= -980.77, z= 45.42 - 1.0, h = 42.69}},
  [143] = {Type = 'market', Pos = {x= 1126.83, y= -982.83, z= 45.42 - 1.0, number = 143 , red = true}},
 
  [44] = {Type = 'market', Pos = {x= 26.32, y= -1347.00, z= 29.50 - 1.0, number = 44}, Object = {x = -528.876, y = 7561.018, z = 6.5205 -1.0, h = 42.69}},
  [144] = {Type = 'market', Pos = {x= 26.02, y= -1339.98, z= 29.50 - 1.0, number = 144 , red = true}},

  [45] = {Type = 'market', Pos = {x = -525.193, y = 7558.474, z = 6.5356 - 1.0, number = 45}, Object = {x= -528.285, y= 7562.361, z= 6.5204 - 1.0, h = 42.69}},
  [145] = {Type = 'market', Pos = {x = -528.188, y = 7562.358, z = 6.5205 - 1.0, number = 144 , red = true}},
 

  [30] = {Type = 'rts', Pos = { x = -1312.43, y = 6964.757, z = 20.518 - 1.0, number = 30}, Object = { x = -1301.98, y = 6967.372, z = 20.517 - 1.0, h = 43.44}},
  [130] = {Type = 'rts', Pos = { x = -1304.10, y = 6965.955, z = 20.517 - 1.0, number = 130 , red = true}},

  [31] = {Type = 'rts', Pos = { x = 167.62446, y = -1057.5743, z = 29.3274 - 1.0, number = 31}, Object = { x = 172.373, y = -1065.7564, z = 29.3274 - 1.0, h = 196.71}},
  [131] = {Type = 'rts', Pos = { x = 172.01446, y = -1063.1243, z = 29.3274 - 1.0, number = 131 , red = true}},

  [32] = {Type = 'rts', Pos = { x = 1951.130, y = 3762.982, z = 32.216 - 1.0, number = 32}, Object = { x = 1951.247, y = 3759.066, z = 32.215 - 1.0, h = 157.88}},
  [132] = {Type = 'rts', Pos = { x = 1952.703, y = 3760.377, z = 32.215 -1.0, number = 132 , red = true}},
  
  [29] = {Type = 'rts', Pos = { x = -439.30, y = -36.96, z = 46.20 - 1.0, number = 29}, Object = { x = -442.47, y = -32.53, z = 40.90 - 1.0, h = 157.88}},
  [129] = {Type = 'rts', Pos = { x = -445.08, y = -36.72, z = 40.90 - 1.0, number = 129 , red = true}},
}